# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

33320:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 33320 --source api/muse_post/grpc_muse_post.proto --lang go --method /ga.api.muse_post.MusePostLogic/GetMusePostList
33321:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 33321 --source api/muse_post/grpc_muse_post.proto --lang go --method /ga.api.muse_post.MusePostLogic/GetMuseCommentList
33322:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 33322 --source api/muse_post/grpc_muse_post.proto --lang go --method /ga.api.muse_post.MusePostLogic/GetMuseContentLikeList
33323:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 33323 --source api/muse_post/grpc_muse_post.proto --lang go --method /ga.api.muse_post.MusePostLogic/GetMusePostDetail
33324:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 33324 --source api/muse_post/grpc_muse_post.proto --lang go --method /ga.api.muse_post.MusePostLogic/MuseAddLike
33325:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 33325 --source api/muse_post/grpc_muse_post.proto --lang go --method /ga.api.muse_post.MusePostLogic/MuseResetLike
33326:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 33326 --source api/muse_post/grpc_muse_post.proto --lang go --method /ga.api.muse_post.MusePostLogic/MusePublishPost
33327:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 33327 --source api/muse_post/grpc_muse_post.proto --lang go --method /ga.api.muse_post.MusePostLogic/MusePublishComment
33328:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 33328 --source api/muse_post/grpc_muse_post.proto --lang go --method /ga.api.muse_post.MusePostLogic/MuseHaveNewPost
33329:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 33329 --source api/muse_post/grpc_muse_post.proto --lang go --method /ga.api.muse_post.MusePostLogic/MarkMuseInteractiveMsgRead
33330:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 33330 --source api/muse_post/grpc_muse_post.proto --lang go --method /ga.api.muse_post.MusePostLogic/GetMuseInteractiveMsg
33331:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 33331 --source api/muse_post/grpc_muse_post.proto --lang go --method /ga.api.muse_post.MusePostLogic/DeleteMuseContent
33332:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 33332 --source api/muse_post/grpc_muse_post.proto --lang go --method /ga.api.muse_post.MusePostLogic/CheckMusePublishRight
33333:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 33333 --source api/muse_post/grpc_muse_post.proto --lang go --method /ga.api.muse_post.MusePostLogic/GetMuseSubCommentList
33334:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 33334 --source api/muse_post/grpc_muse_post.proto --lang go --method /ga.api.muse_post.MusePostLogic/GetMuseUserRecord
33335:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 33335 --source api/muse_post/grpc_muse_post.proto --lang go --method /ga.api.muse_post.MusePostLogic/GetMusePostAtFriends
