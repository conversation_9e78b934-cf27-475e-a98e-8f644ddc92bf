apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-wish-list-logic
spec:
  gateways:
  - istio-system/apiv2-gateway
  - istio-system/apiv2-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.wish_list.WishListLogic/
    rewrite:
      uri: /logic.WishListLogic/
    delegate:
       name: wish-list-logic-delegator-80
       namespace: quicksilver


