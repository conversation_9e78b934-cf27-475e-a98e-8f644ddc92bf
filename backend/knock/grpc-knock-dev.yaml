apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-knock-logic
spec:
  gateways:
  - istio-system/apiv2-gateway
  - istio-system/apiv2-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.knock.KnockLogic/
    rewrite:
      uri: /logic.KnockLogic/
    delegate:
       name: knocklogic-delegator-80
       namespace: quicksilver


