# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

5070:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 5070 --source api/gnobility/grpc_gnobility.proto --lang go --method /ga.api.gnobility.GnobilityLogic/SetNobilitySwitch
5071:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 5071 --source api/gnobility/grpc_gnobility.proto --lang go --method /ga.api.gnobility.GnobilityLogic/GetNobilitySwitchFlag
