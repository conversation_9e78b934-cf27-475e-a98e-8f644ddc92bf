# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

10:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 10 --source api/auth/grpc_auth.proto --lang go --method /ga.api.auth.AuthLogic/Auth
12:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 12 --source api/auth/grpc_auth.proto --lang go --method /ga.api.auth.AuthLogic/Reg
14:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 14 --source api/auth/grpc_auth.proto --lang go --method /ga.api.auth.AuthLogic/AccountVerifyCode
166:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 166 --source api/auth/grpc_auth.proto --lang go --method /ga.api.auth.AuthLogic/GetGameConfig
210:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 210 --source api/auth/grpc_auth.proto --lang go --method /ga.api.auth.AuthLogic/ThirdPartyAuth
211:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 211 --source api/auth/grpc_auth.proto --lang go --method /ga.api.auth.AuthLogic/ThirdPartyReg
214:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 214 --source api/auth/grpc_auth.proto --lang go --method /ga.api.auth.AuthLogic/ThirdpartyVerifyCheck
232:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 232 --source api/auth/grpc_auth.proto --lang go --method /ga.api.auth.AuthLogic/SubmitVerifyCode
30380:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 30380 --source api/auth/grpc_auth.proto --lang go --method /ga.api.auth.AuthLogic/ImActivityCenterEntrance
404:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 404 --source api/auth/grpc_auth.proto --lang go --method /ga.api.auth.AuthLogic/BindPhoneBeforeAuth
405:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 405 --source api/auth/grpc_auth.proto --lang go --method /ga.api.auth.AuthLogic/BindPhoneAfterAuth
5050:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 5050 --source api/auth/grpc_auth.proto --lang go --method /ga.api.auth.AuthLogic/AccountVoiceVerifyCode
6600:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 6600 --source api/auth/grpc_auth.proto --lang go --method /ga.api.auth.AuthLogic/GetPcAuthApplyResult
6601:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 6601 --source api/auth/grpc_auth.proto --lang go --method /ga.api.auth.AuthLogic/GetPcAuthApply
6602:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 6602 --source api/auth/grpc_auth.proto --lang go --method /ga.api.auth.AuthLogic/ProcPcAuthApply
6603:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 6603 --source api/auth/grpc_auth.proto --lang go --method /ga.api.auth.AuthLogic/GetPcAuthApplyResultV2
6604:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 6604 --source api/auth/grpc_auth.proto --lang go --method /ga.api.auth.AuthLogic/ProcPcAuthApplyBeforeAuth
