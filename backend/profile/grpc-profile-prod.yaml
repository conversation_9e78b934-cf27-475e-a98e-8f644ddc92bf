apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-profile-logic
spec:
  gateways:
  - quicksilver/tt-api-gateway
  - quicksilver/tt-api-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.profile.ProfileLogic/
    rewrite:
      uri: /logic.profile.ProfileLogic/
    delegate:
       name: profile-logic-delegator-80
       namespace: quicksilver


