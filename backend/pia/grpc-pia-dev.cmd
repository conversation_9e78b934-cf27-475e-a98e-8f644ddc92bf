# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

33021:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33021 --source api/pia/grpc_pia.proto --lang go --method /ga.api.pia.PiaLogic/GetChannelPiaStatus
33022:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33022 --source api/pia/grpc_pia.proto --lang go --method /ga.api.pia.PiaLogic/SetPiaSwitch
33023:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33023 --source api/pia/grpc_pia.proto --lang go --method /ga.api.pia.PiaLogic/GetDrama
33024:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33024 --source api/pia/grpc_pia.proto --lang go --method /ga.api.pia.PiaLogic/GetSearchOptionGroup
33026:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33026 --source api/pia/grpc_pia.proto --lang go --method /ga.api.pia.PiaLogic/GetCurrentPiaInfo
33027:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33027 --source api/pia/grpc_pia.proto --lang go --method /ga.api.pia.PiaLogic/SetPiaPhase
33028:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33028 --source api/pia/grpc_pia.proto --lang go --method /ga.api.pia.PiaLogic/SetPiaProgress
33029:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33029 --source api/pia/grpc_pia.proto --lang go --method /ga.api.pia.PiaLogic/GetPlayingChannel
33030:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33030 --source api/pia/grpc_pia.proto --lang go --method /ga.api.pia.PiaLogic/GetQualityDramaList
33031:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33031 --source api/pia/grpc_pia.proto --lang go --method /ga.api.pia.PiaLogic/GetPracticeDramaList
33032:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33032 --source api/pia/grpc_pia.proto --lang go --method /ga.api.pia.PiaLogic/SelectDrama
33033:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33033 --source api/pia/grpc_pia.proto --lang go --method /ga.api.pia.PiaLogic/SetBgmInfo
33034:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33034 --source api/pia/grpc_pia.proto --lang go --method /ga.api.pia.PiaLogic/GetBgmInfo
33035:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33035 --source api/pia/grpc_pia.proto --lang go --method /ga.api.pia.PiaLogic/SetCompereMic
33036:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33036 --source api/pia/grpc_pia.proto --lang go --method /ga.api.pia.PiaLogic/OrderDrama
33037:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33037 --source api/pia/grpc_pia.proto --lang go --method /ga.api.pia.PiaLogic/GetOrderDramaList
33038:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33038 --source api/pia/grpc_pia.proto --lang go --method /ga.api.pia.PiaLogic/DeleteOrderDrama
33039:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33039 --source api/pia/grpc_pia.proto --lang go --method /ga.api.pia.PiaLogic/PiaSelectRole
33040:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33040 --source api/pia/grpc_pia.proto --lang go --method /ga.api.pia.PiaLogic/PiaCancelSelectRole
33041:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33041 --source api/pia/grpc_pia.proto --lang go --method /ga.api.pia.PiaLogic/SelectDramaV2
33042:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33042 --source api/pia/grpc_pia.proto --lang go --method /ga.api.pia.PiaLogic/PiaOperateDrama
33043:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33043 --source api/pia/grpc_pia.proto --lang go --method /ga.api.pia.PiaLogic/PiaGetDramaStatus
33044:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33044 --source api/pia/grpc_pia.proto --lang go --method /ga.api.pia.PiaLogic/PiaOperateBgm
33045:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33045 --source api/pia/grpc_pia.proto --lang go --method /ga.api.pia.PiaLogic/GetSearchOptionGroupV2
33046:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33046 --source api/pia/grpc_pia.proto --lang go --method /ga.api.pia.PiaLogic/GetDramaList
33047:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33047 --source api/pia/grpc_pia.proto --lang go --method /ga.api.pia.PiaLogic/GetDramaDetailById
33048:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33048 --source api/pia/grpc_pia.proto --lang go --method /ga.api.pia.PiaLogic/PiaGetDramaCopyId
33049:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33049 --source api/pia/grpc_pia.proto --lang go --method /ga.api.pia.PiaLogic/PiaCreateDramaCopy
33050:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33050 --source api/pia/grpc_pia.proto --lang go --method /ga.api.pia.PiaLogic/PiaOperateBgmVol
33051:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33051 --source api/pia/grpc_pia.proto --lang go --method /ga.api.pia.PiaLogic/DoUserDramaCollect
33052:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33052 --source api/pia/grpc_pia.proto --lang go --method /ga.api.pia.PiaLogic/GetUserDramaCollection
33053:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33053 --source api/pia/grpc_pia.proto --lang go --method /ga.api.pia.PiaLogic/GetPlayingChannelV2
33054:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33054 --source api/pia/grpc_pia.proto --lang go --method /ga.api.pia.PiaLogic/PiaChangePlayType
33055:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33055 --source api/pia/grpc_pia.proto --lang go --method /ga.api.pia.PiaLogic/GetMyDramaPlayingRecord
33056:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33056 --source api/pia/grpc_pia.proto --lang go --method /ga.api.pia.PiaLogic/PiaBatchDeleteMyPlayingRecord
33057:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33057 --source api/pia/grpc_pia.proto --lang go --method /ga.api.pia.PiaLogic/PiaGetRankingList
33058:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33058 --source api/pia/grpc_pia.proto --lang go --method /ga.api.pia.PiaLogic/PiaGetMyPlayingRecordIdList
33059:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33059 --source api/pia/grpc_pia.proto --lang go --method /ga.api.pia.PiaLogic/GetMyDramaCopyList
33060:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33060 --source api/pia/grpc_pia.proto --lang go --method /ga.api.pia.PiaLogic/PiaCopyDramaList
33061:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33061 --source api/pia/grpc_pia.proto --lang go --method /ga.api.pia.PiaLogic/PiaConfirmCoverCopy
33062:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33062 --source api/pia/grpc_pia.proto --lang go --method /ga.api.pia.PiaLogic/SetDramaCopyStatus
33063:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33063 --source api/pia/grpc_pia.proto --lang go --method /ga.api.pia.PiaLogic/DeleteDramaCopy
33064:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33064 --source api/pia/grpc_pia.proto --lang go --method /ga.api.pia.PiaLogic/PiaCreateDramaCopyV2
33065:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33065 --source api/pia/grpc_pia.proto --lang go --method /ga.api.pia.PiaLogic/PiaPerformDrama
33066:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33066 --source api/pia/grpc_pia.proto --lang go --method /ga.api.pia.PiaLogic/PiaSendDialogueIndex
33067:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33067 --source api/pia/grpc_pia.proto --lang go --method /ga.api.pia.PiaLogic/PiaFollowMic
33068:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33068 --source api/pia/grpc_pia.proto --lang go --method /ga.api.pia.PiaLogic/PiaReportDialogueIndex
33069:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33069 --source api/pia/grpc_pia.proto --lang go --method /ga.api.pia.PiaLogic/PiaGetPreviousDialogueIndex
33070:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33070 --source api/pia/grpc_pia.proto --lang go --method /ga.api.pia.PiaLogic/PiaUnFollowMic
33071:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33071 --source api/pia/grpc_pia.proto --lang go --method /ga.api.pia.PiaLogic/PiaGetMyFollowInfo
33072:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33072 --source api/pia/grpc_pia.proto --lang go --method /ga.api.pia.PiaLogic/PiaGetFollowedStatusOfMicList
