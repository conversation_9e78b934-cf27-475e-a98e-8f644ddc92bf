apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-concert-logic
spec:
  gateways:
  - quicksilver/tt-api-gateway
  - quicksilver/tt-api-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.concert.ConcertLogic/
    rewrite:
      uri: /logic.ConcertLogic/
    delegate:
       name: concert-logic-delegator-80
       namespace: quicksilver


