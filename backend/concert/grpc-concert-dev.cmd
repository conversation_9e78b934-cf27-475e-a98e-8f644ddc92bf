# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

33300:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33300 --source api/concert/grpc_concert.proto --lang go --method /ga.api.concert.ConcertLogic/GetConcertSongOpts
33301:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33301 --source api/concert/grpc_concert.proto --lang go --method /ga.api.concert.ConcertLogic/SearchConcertSong
33302:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33302 --source api/concert/grpc_concert.proto --lang go --method /ga.api.concert.ConcertLogic/GetConcertSongList
33303:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33303 --source api/concert/grpc_concert.proto --lang go --method /ga.api.concert.ConcertLogic/GetAllConcertResource
33304:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33304 --source api/concert/grpc_concert.proto --lang go --method /ga.api.concert.ConcertLogic/StartConcertSinging
33305:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33305 --source api/concert/grpc_concert.proto --lang go --method /ga.api.concert.ConcertLogic/GetMusicBook
33306:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33306 --source api/concert/grpc_concert.proto --lang go --method /ga.api.concert.ConcertLogic/CompleteDownloadingMusicBook
33307:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33307 --source api/concert/grpc_concert.proto --lang go --method /ga.api.concert.ConcertLogic/StopConcertSinging
33308:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33308 --source api/concert/grpc_concert.proto --lang go --method /ga.api.concert.ConcertLogic/GetConcertInfo
33309:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33309 --source api/concert/grpc_concert.proto --lang go --method /ga.api.concert.ConcertLogic/UpdateBackingTrackStatus
33310:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33310 --source api/concert/grpc_concert.proto --lang go --method /ga.api.concert.ConcertLogic/ReportConcertSuccCount
33311:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33311 --source api/concert/grpc_concert.proto --lang go --method /ga.api.concert.ConcertLogic/JoinConcert
33312:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33312 --source api/concert/grpc_concert.proto --lang go --method /ga.api.concert.ConcertLogic/GetAllConcertImage
33313:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33313 --source api/concert/grpc_concert.proto --lang go --method /ga.api.concert.ConcertLogic/SetConcertUserImage
33314:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33314 --source api/concert/grpc_concert.proto --lang go --method /ga.api.concert.ConcertLogic/GetAllConcertOnMicUserImage
33317:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33317 --source api/concert/grpc_concert.proto --lang go --method /ga.api.concert.ConcertLogic/GetConcertSongById
33318:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 33318 --source api/concert/grpc_concert.proto --lang go --method /ga.api.concert.ConcertLogic/GetRecentUploadedSong
