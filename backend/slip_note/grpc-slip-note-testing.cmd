# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

30581:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 30581 --source api/slip_note/grpc_slip_note.proto --lang go --method /ga.api.slip_note.SlipNoteLogic/GetSlipNoteConfig
30582:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 30582 --source api/slip_note/grpc_slip_note.proto --lang go --method /ga.api.slip_note.SlipNoteLogic/PublishSlipNote
30583:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 30583 --source api/slip_note/grpc_slip_note.proto --lang go --method /ga.api.slip_note.SlipNoteLogic/CloseSlipNote
30584:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 30584 --source api/slip_note/grpc_slip_note.proto --lang go --method /ga.api.slip_note.SlipNoteLogic/GetSlipNoteStatus
30585:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 30585 --source api/slip_note/grpc_slip_note.proto --lang go --method /ga.api.slip_note.SlipNoteLogic/PickSlipNote
30586:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 30586 --source api/slip_note/grpc_slip_note.proto --lang go --method /ga.api.slip_note.SlipNoteLogic/SetSlipNote
30587:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 30587 --source api/slip_note/grpc_slip_note.proto --lang go --method /ga.api.slip_note.SlipNoteLogic/FetchSlipNote
30588:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 30588 --source api/slip_note/grpc_slip_note.proto --lang go --method /ga.api.slip_note.SlipNoteLogic/CommentToSlipNote
30589:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 30589 --source api/slip_note/grpc_slip_note.proto --lang go --method /ga.api.slip_note.SlipNoteLogic/PullSlipNoteCommentList
30590:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 30590 --source api/slip_note/grpc_slip_note.proto --lang go --method /ga.api.slip_note.SlipNoteLogic/ReportSlipNoteStatus
30591:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 30591 --source api/slip_note/grpc_slip_note.proto --lang go --method /ga.api.slip_note.SlipNoteLogic/GetAccountsForSlipNote
