# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

31310:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 31310 --source api/collection/grpc_collection.proto --lang go --method /ga.api.collection.CollectionLogic/ReportStayAddTicket
31329:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 31329 --source api/collection/grpc_collection.proto --lang go --method /ga.api.collection.CollectionLogic/GetRhythmSwitch
31330:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 31330 --source api/collection/grpc_collection.proto --lang go --method /ga.api.collection.CollectionLogic/SetRhythmSwitch
31332:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 31332 --source api/collection/grpc_collection.proto --lang go --method /ga.api.collection.CollectionLogic/UpdateCommonPhotoAlbum
31336:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 31336 --source api/collection/grpc_collection.proto --lang go --method /ga.api.collection.CollectionLogic/GetMusicZoneTemplate
31337:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 31337 --source api/collection/grpc_collection.proto --lang go --method /ga.api.collection.CollectionLogic/UserDefinedVotePKStart
31338:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 31338 --source api/collection/grpc_collection.proto --lang go --method /ga.api.collection.CollectionLogic/GetUserDefinedVotePK
31339:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 31339 --source api/collection/grpc_collection.proto --lang go --method /ga.api.collection.CollectionLogic/UserDefinedVote
31340:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 31340 --source api/collection/grpc_collection.proto --lang go --method /ga.api.collection.CollectionLogic/UserDefinedVotePKCancel
31344:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 31344 --source api/collection/grpc_collection.proto --lang go --method /ga.api.collection.CollectionLogic/SetVoiceWatermark
31345:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 31345 --source api/collection/grpc_collection.proto --lang go --method /ga.api.collection.CollectionLogic/GetVoiceWatermark
31346:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 31346 --source api/collection/grpc_collection.proto --lang go --method /ga.api.collection.CollectionLogic/DelVoiceWatermark
31347:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 31347 --source api/collection/grpc_collection.proto --lang go --method /ga.api.collection.CollectionLogic/SendReadVoice
31349:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 31349 --source api/collection/grpc_collection.proto --lang go --method /ga.api.collection.CollectionLogic/PostAIRapperPost
31350:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 31350 --source api/collection/grpc_collection.proto --lang go --method /ga.api.collection.CollectionLogic/GetTabInfo
31351:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 31351 --source api/collection/grpc_collection.proto --lang go --method /ga.api.collection.CollectionLogic/GetLyricInfo
31352:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 31352 --source api/collection/grpc_collection.proto --lang go --method /ga.api.collection.CollectionLogic/GetUserAIRapperLevel
31353:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 31353 --source api/collection/grpc_collection.proto --lang go --method /ga.api.collection.CollectionLogic/Aggregation
31354:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 31354 --source api/collection/grpc_collection.proto --lang go --method /ga.api.collection.CollectionLogic/GetQuestionnaire
31355:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 31355 --source api/collection/grpc_collection.proto --lang go --method /ga.api.collection.CollectionLogic/GetH5UrlWithAICtx
31356:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 31356 --source api/collection/grpc_collection.proto --lang go --method /ga.api.collection.CollectionLogic/GetMusicBlockFilter
31357:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 31357 --source api/collection/grpc_collection.proto --lang go --method /ga.api.collection.CollectionLogic/UpgradeUserAIRapperLevel
31358:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 31358 --source api/collection/grpc_collection.proto --lang go --method /ga.api.collection.CollectionLogic/RhythmGetRecommendRoom
31359:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 31359 --source api/collection/grpc_collection.proto --lang go --method /ga.api.collection.CollectionLogic/RhythmGetPosts
31365:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 31365 --source api/collection/grpc_collection.proto --lang go --method /ga.api.collection.CollectionLogic/SetMicSort
31366:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 31366 --source api/collection/grpc_collection.proto --lang go --method /ga.api.collection.CollectionLogic/GetMicSort
31367:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 31367 --source api/collection/grpc_collection.proto --lang go --method /ga.api.collection.CollectionLogic/SwitchMicSort
31376:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 31376 --source api/collection/grpc_collection.proto --lang go --method /ga.api.collection.CollectionLogic/GetCyberWorldHome
31377:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 31377 --source api/collection/grpc_collection.proto --lang go --method /ga.api.collection.CollectionLogic/GetBackgroundVideoUrl
31700:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 31700 --source api/collection/grpc_collection.proto --lang go --method /ga.api.collection.CollectionLogic/GetForceTopic
