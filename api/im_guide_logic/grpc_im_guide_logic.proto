syntax = "proto3";
package ga.api.im_guide_logic;

import "im_guide_logic/im_guide_logic.proto";
import "api/extension/extension.proto";

option java_package      = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";
option go_package = "golang.52tt.com/protocol/app/api/im_guide_logic;im_guide_logic";

service ImGuideLogic {
    option (ga.api.extension.logic_service_name) = "im-guide-logic";
    option (ga.api.extension.logic_service_language) = "go";

    // 获取预约入口信息
    rpc GetGameAppointmentEntrance(ga.im_guide_logic.GetGameAppointmentEntranceReq) returns (ga.im_guide_logic.GetGameAppointmentEntranceResp) {
        option (ga.api.extension.command) = {
             id: 6051;
        };
    }

    // 发送邀请
    rpc SendGameAppointment(ga.im_guide_logic.SendGameAppointmentReq) returns (ga.im_guide_logic.SendGameAppointmentResp) {
        option (ga.api.extension.command) = {
             id: 6052;
        };
    }

    // 处理邀请
    rpc HandleGameAppointment(ga.im_guide_logic.HandleGameAppointmentReq) returns (ga.im_guide_logic.HandleGameAppointmentResp) {
        option (ga.api.extension.command) = {
            id: 6053;
        };
    }

    // 获取预约详情信息
//    rpc GetGameAppointmentDetail(ga.im_guide_logic.GetGameAppointmentDetailReq) returns (ga.im_guide_logic.GetGameAppointmentDetailResp) {
//        option (ga.api.extension.command) = {
//            id: 6054;
//        };
//    }

    // 设置预约提醒
    rpc SetGameAppointmentRemind(ga.im_guide_logic.SetGameAppointmentRemindReq) returns (ga.im_guide_logic.SetGameAppointmentRemindResp) {
        option (ga.api.extension.command) = {
            id: 6055;
        };
    }
    // 会话来源上报
    rpc MsgSourceTypeReport(ga.im_guide_logic.MsgSourceTypeReportReq) returns (ga.im_guide_logic.MsgSourceTypeReportResp) {
        option (ga.api.extension.command) = {
            id: 6056;
        };
    }

    // 获取im开聊匹配文案
    rpc GetImBeginChatSuggestion(ga.im_guide_logic.GetImBeginChatSuggestionReq) returns (ga.im_guide_logic.GetImBeginChatSuggestionResp) {
        option (ga.api.extension.command) = {
            id: 6057;
        };
    }

    // 获取接话聊天匹配推荐回复
    rpc GetImReplyChatSuggestion(ga.im_guide_logic.GetImReplyChatSuggestionReq) returns (ga.im_guide_logic.GetImReplyChatSuggestionResp) {
        option (ga.api.extension.command) = {
            id: 6058;
        };
    }

    // const unsigned int CMD_SetUserOnlineRemind = 6059; // 设置用户上线提醒
    rpc SetUserOnlineRemind(ga.im_guide_logic.SetUserOnlineRemindReq) returns (ga.im_guide_logic.SetUserOnlineRemindResp) {
        option (ga.api.extension.command) = {
            id: 6059;
        };
    }

    // const unsigned int CMD_GetOnlineRemindSetting = 6060; // 获取用户上线提醒设置
    rpc GetOnlineRemindSetting(ga.im_guide_logic.GetOnlineRemindSettingReq) returns (ga.im_guide_logic.GetOnlineRemindSettingResp) {
        option (ga.api.extension.command) = {
            id: 6060;
        };
    }

    // const unsigned int CMD_IsShowLoginRemindGuide = 6061; // 是否展示上线提醒引导
    rpc IsShowLoginRemindGuide(ga.im_guide_logic.IsShowLoginRemindGuideReq) returns (ga.im_guide_logic.IsShowLoginRemindGuideResp) {
        option (ga.api.extension.command) = {
            id: 6061;
        };
    }

}