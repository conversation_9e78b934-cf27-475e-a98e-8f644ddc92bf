syntax = "proto3";

package sendim_bot;

option go_package = "golang.52tt.com/protocol/services/sendim/sendim-bot";

service SendImBot {
  // 发送消息
  rpc SendTask (SendTaskReq) returns (SendTaskResp) {}
  // 停止发送，已收到的用户还会继续看到消息，未收到的用户不会看到消息
  rpc StopTask (StopTaskReq) returns (StopTaskResp) {}
  // 撤回全部消息，包括未读和已读全部撤回，消息会直接删除不会有任何提示
  rpc RecallTask (RecallTaskReq) returns (RecallTaskResp) {}
}

enum ErrCode {
  ERR_OK = 0;
  ERR_PARAM = 1;
  ERR_SYSTEM = 2;
}

// 发送任务请求
message SendTaskReq {
  // 任务id，同一任务的分批请求使用相同task值，不同任务请务必保证其值全局唯一
  // 命名规则: 随机值-appname-时间戳毫秒，保证唯一，appname 为空时填 all
  string task = 1;
  // 发送者
  Sender sender = 2;
  // 接收者
  Receiver receiver = 3;
  // IM消息结构
  ImMsg msg = 4;
  // 离线推送消息内容，仅对于sender和receiver类型都为user的情况有效
  OfflinePush offline_push = 5;
}

// 发送任务应答
message SendTaskResp {
  // 错误码
  ErrCode err = 1;
  // 结果描述
  string msg = 2;
}

// 停止发送任务请求
message StopTaskReq {
  // 任务id，与SendTaskReq的task对应
  string task = 1;
}

// 停止发送任务应答
message StopTaskResp {
  // 错误码
  ErrCode err = 1;
  // 结果描述
  string msg = 2;
}

// 撤回任务请求
message RecallTaskReq {
  // 任务id，与SendTaskReq的task对应
  string task = 1;
}

// 撤回任务应答
message RecallTaskResp {
  // 错误码
  ErrCode err = 1;
  // 结果描述
  string msg = 2;
}

// 离线推送内容
message OfflinePush {
  // 标题，可选，默认为发送者昵称
  string title = 1;
  // 展示文本
  string content = 2;
  // 跳转链接
  string jump_url = 3;
}

// IM消息结构
message ImMsg {
  // 消息内容
  Content content = 1;
  // 客户端消息id
  uint32 client_msg_id = 2;
  // 客户端消息时间
  uint32 client_msg_time = 3;
  // app名称
  string app_name = 4;     // 取值: ttvoice/huanyou/maike, 空表示所有 (取决于synclogic过滤机制)
  // 客户端平台
  string app_platform = 5; // 取值: android/ios/pc, 空表示全平台 (取决于synclogic过滤机制)
  // 过期时间
  uint32 expired_at = 6;
}

// 发送者
message Sender {
  // 发送者类型枚举定义
  enum SenderType {
    // 未知
    Unknown = 0;
    // 仅限用户间1v1消息
    User = 1;
    // 公众号/广播消息
    Public = 2;
  }
  // 发送者类型
  SenderType type = 1;
  // 发送者的用户id，如果SenderType为Public，请填写对应环境（比如云测与生产相同名字的公众号id不同)的正常用户id
  uint32 id = 2;
}

// 接收者
message Receiver {
  // 接收者类型枚举定义
  enum ReceiverType {
    // 未知
    Unknown = 0;
    // 仅限用户间1v1消息
    User = 1;
    // 公众号消息
    Public = 2;
    // 广播消息
    Broadcast = 3;
  }
  // 接收者类型
  ReceiverType type = 1;
  // 接收者的uid，type=user，长度 >= 1(取决于发送方类型); type=Public/Broadcast, 长度为 0
  repeated uint32 id_list = 2;
}

// 消息内容定义
message Content {
  // 消息内容类型枚举定义
  enum ContentType {
    // 未知
    Unknown = 0;
    // 最普通的文本，对应 proto/pbfile/im.proto TEXT_MSG (1)
    Text = 1;
    // 带高亮和跳转的文本，对应 proto/pbfile/im.proto TT_COMMON_TEXT_NOTIFY (37)
    TextWithHighlightUrl = 2;
    // 带图片、标题、跳转、内容等的公众号推送消息，对应 src/proto/pbfile/im.proto OFFICAIL_MESSAGE_SINGLE(22)/OFFICAIL_MESSAGE_BUNCH(23)
    OfficialMessage = 3;
    // 扩展字段 对应 proto/pbfile/im.proto NEW_EXTENDED (21)
    NewExtended = 4;
    // 通用透传消息
    Common = 5;
  }
  // 消息内容类型
  ContentType type = 1;
  // 普通文本消息
  ImTextNormal text_normal = 2; // IM_CONTENT_TEXT &&
  // 带有高亮及url的文本消息
  ImTextWithHighlightUrl text_hl_url = 3;
  // 官方号/公众号消息
  ImOfficialMessage official_message = 4; // 对应 proto/pbfile/ga_base.proto : OfficialMessageSingle、OfficialMessageBunch
  // 新扩展消息类型，一般用不到
  ImNewExtended new_extended = 5;
  // 通用透传消息
  CommonMsg common = 6; // ContentType Common
}

// 通用透传消息
message CommonMsg {
  // 消息类型 see ga.IM_MSG_TYPE
  uint32 msg_type = 1;
  // 文本
  string content = 2;
  // 自定义扩展
  bytes ext = 3;
  // 消息来源类型 see ga.MsgSourceType
  uint32 msg_source_type = 4;
}

// Content::Text
message ImTextNormal{
  // 普通文本
  string content = 1;
}

// Content::TextWithHighlightUrl
message ImTextWithHighlightUrl{
  // 文本内容
  string content = 1;
  // 高亮内容
  string highlight = 2;
  // 跳转url
  string url = 3;
}

// Content::IM_CONTENT_NEW_EXTENDED
message ImNewExtended {
  string content = 1;
}

// Content::OfficialMessage
message ImOfficialMessage {
  // 消息ID
  uint32 message_id = 1;
  // 官方消息体
  repeated OfficialMessageBody messages = 2;
}

// 对应 proto/pbfile/ga_base.proto : OfficialMessageBody
message OfficialMessageBody {
  // 官方消息类型枚举定义
  enum OfficialMessageType {
    // 未知
    Unknown = 0;
    // 文本
    TEXT = 1;
    // 图文消息
    PICTURE_AND_TEXT = 2;
    // 游戏下载
    GAME_DOWNLOAD = 3;
    // 文本带url跳转
    TEXT_JUMP = 4;
  }
  // 官方消息类型
  OfficialMessageType type = 1;
  // 标题
  string title = 2;
  // 日期
  uint32 date = 3;
  // 消息主体内容
  string content = 4;
  // 摘要，简介
  string intro = 5;
  // 图片
  string image = 6;
  // 控制模块，如控制跳转
  ControlBlock ctrl_block = 7;
  // 允许重传
  uint32 allow_retransmit = 8;
}

// 对应 proto/pbfile/ga_base.proto : ControlBlock
message ControlBlock{
  // 控制命令
  uint32 cmd = 1;
  // 控制内容, 当cmd值为1时，cmd_body填proto/pbfile/official_account.proto的InAppNavigationCommand序列化后的内容
  bytes cmd_body = 2;
}

// 后台专用，不用管
message ImAccount {
  uint32 id = 1;
  string account = 2;
  string nickname = 3;
  string alias = 4;
}

// 后台专用，不用管
message Receipt {
  enum SuffixType {
    Unknown = 0;
    User = 1;
    Public = 2;
    Broadcast = 3;
  }
  uint32 id = 1;
  uint32 seq = 2;
  SuffixType type = 3;
  uint32 from_id = 4;
  uint32 client_msg_id = 5;
  uint32 svr_msg_id = 6;
  string from_name = 7;
  string to_name = 8;
}
