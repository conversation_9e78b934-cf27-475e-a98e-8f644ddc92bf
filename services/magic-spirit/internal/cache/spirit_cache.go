package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/services/magic-spirit/internal/mysql"
)

const (
	MagicSpiritExpireTime = 3 * time.Second
)

func (c *Cache) GetMagicSpirit(ctx context.Context) ([]*mysql.MagicSpirit, error) {
	cache, err := c.redisCli.HGetAll(ctx, genMagicSpiritKey()).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMagicSpirit by cache error: %v", err)
		return nil, err
	}

	data := make([]*mysql.MagicSpirit, 0, 8)
	for _, v := range cache {
		item := &mysql.MagicSpirit{}
		err := json.Unmarshal([]byte(v), &item)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetMagicSpirit convert struct error: %v", err)
			return nil, err
		}
		data = append(data, item)
	}

	return data, nil
}

func (c *Cache) GetMagicSpiritById(ctx context.Context, magicSpiritId uint32) (*mysql.MagicSpirit, bool, error) {
	ms := &mysql.MagicSpirit{}

	rs, err := c.redisCli.HGet(ctx, genMagicSpiritKey(), strconv.FormatInt(int64(magicSpiritId), 10)).Result()
	if err != nil {
		if err == redis.Nil {
			return ms, false, nil
		}
		log.ErrorWithCtx(ctx, "GetMagicSpiritById error: %v", err)
		return ms, false, err
	}

	err = json.Unmarshal([]byte(rs), &ms)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMagicSpiritById unmarshal error: %v", err)
		return ms, false, err
	}

	return ms, true, nil
}

func (c *Cache) SetMagicSpirit(ctx context.Context, data []*mysql.MagicSpirit) error {
	if len(data) == 0 {
		return nil
	}

	magicSpiritMap := make(map[string]interface{})
	for _, item := range data {
		bytes, err := json.Marshal(item)
		if err != nil {
			log.ErrorWithCtx(ctx, "SetMagicSpirit marshal json error: %v, item:%v", err, item)
			return err
		}
		magicId := fmt.Sprint(item.MagicSpiritId)
		magicSpiritMap[magicId] = string(bytes)
	}

	err := c.redisCli.HMSet(ctx, genMagicSpiritKey(), magicSpiritMap).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "SetMagicSpirit cache error: %v", err)
		return err
	}

	return nil
}

func (c *Cache) DelMagicSpiritCache(ctx context.Context) error {
	err := c.redisCli.Expire(ctx, genMagicSpiritKey(), time.Second*5).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "DelMagicSpiritCache delete magicSpirit cache error: %v", err)
		return err
	}

	err = c.redisCli.Expire(ctx, genMagicSpiritPondKey(), time.Second*1).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "DelMagicSpiritCache delete magicSpiritPond cache error： %v", err)
	}

	// 标记缓存有新版本
	err = c.UpdateMagicSpiritVersion(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetMagicSpirit update version error: %v", err)
		return err
	}

	log.InfoWithCtx(ctx, "DelMagicSpiritCache success")
	return nil
}

func (c *Cache) UpdateMagicSpiritVersion(ctx context.Context) error {
	err := c.redisCli.Set(ctx, genMagicSpiritKeyVersionKey(), time.Now().Unix(), 0).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "SetMagicSpirit update version error: %v", err)
		return err
	}

	return nil
}

func (c *Cache) GetUpdateMagicSpiritVersion(ctx context.Context) (uint32, error) {
	rs, err := c.redisCli.Get(ctx, genMagicSpiritKeyVersionKey()).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUpdateMagicSpiritVersion error: %v", err)
		return 0, err
	}

	version, err := strconv.ParseInt(rs, 10, 64)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUpdateMagicSpiritVersion parse int error: %v", err)
		return 0, err
	}

	return uint32(version), nil
}

func (c *Cache) ExpireMagicSpirit(ctx context.Context) error {
	err := c.redisCli.Expire(ctx, genMagicSpiritKey(), MagicSpiritExpireTime).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUpdateMagicSpiritVersion expire error: %v", err)
		return err
	}

	return nil
}
