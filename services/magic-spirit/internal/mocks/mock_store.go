// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/magic-spirit/internal/mysql (interfaces: IStore)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
	gorm "github.com/jinzhu/gorm"
	mysql "golang.52tt.com/services/magic-spirit/internal/mysql"
)

// MockIStore is a mock of IStore interface.
type MockIStore struct {
	ctrl     *gomock.Controller
	recorder *MockIStoreMockRecorder
}

// MockIStoreMockRecorder is the mock recorder for MockIStore.
type MockIStoreMockRecorder struct {
	mock *MockIStore
}

// NewMockIStore creates a new mock instance.
func NewMockIStore(ctrl *gomock.Controller) *MockIStore {
	mock := &MockIStore{ctrl: ctrl}
	mock.recorder = &MockIStoreMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIStore) EXPECT() *MockIStoreMockRecorder {
	return m.recorder
}

// AddBlacklist mocks base method.
func (m *MockIStore) AddBlacklist(arg0 context.Context, arg1 []*mysql.MagicSpiritBlacklist) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddBlacklist", arg0, arg1)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddBlacklist indicates an expected call of AddBlacklist.
func (mr *MockIStoreMockRecorder) AddBlacklist(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddBlacklist", reflect.TypeOf((*MockIStore)(nil).AddBlacklist), arg0, arg1)
}

// AddMagicSpirit mocks base method.
func (m *MockIStore) AddMagicSpirit(arg0 context.Context, arg1 *mysql.MagicSpirit) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddMagicSpirit", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddMagicSpirit indicates an expected call of AddMagicSpirit.
func (mr *MockIStoreMockRecorder) AddMagicSpirit(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddMagicSpirit", reflect.TypeOf((*MockIStore)(nil).AddMagicSpirit), arg0, arg1)
}

// AddMagicSpiritPond mocks base method.
func (m *MockIStore) AddMagicSpiritPond(arg0 context.Context, arg1 *gorm.DB, arg2 []*mysql.MagicSpiritPond) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddMagicSpiritPond", arg0, arg1, arg2)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddMagicSpiritPond indicates an expected call of AddMagicSpiritPond.
func (mr *MockIStoreMockRecorder) AddMagicSpiritPond(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddMagicSpiritPond", reflect.TypeOf((*MockIStore)(nil).AddMagicSpiritPond), arg0, arg1, arg2)
}

// AddMagicSpiritTmp mocks base method.
func (m *MockIStore) AddMagicSpiritTmp(arg0 context.Context, arg1 *mysql.MagicSpiritTemporary) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddMagicSpiritTmp", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddMagicSpiritTmp indicates an expected call of AddMagicSpiritTmp.
func (mr *MockIStoreMockRecorder) AddMagicSpiritTmp(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddMagicSpiritTmp", reflect.TypeOf((*MockIStore)(nil).AddMagicSpiritTmp), arg0, arg1)
}

// AddNormalPondTmp mocks base method.
func (m *MockIStore) AddNormalPondTmp(arg0 context.Context, arg1 []*mysql.MagicSpiritPondTmp) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddNormalPondTmp", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddNormalPondTmp indicates an expected call of AddNormalPondTmp.
func (mr *MockIStoreMockRecorder) AddNormalPondTmp(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddNormalPondTmp", reflect.TypeOf((*MockIStore)(nil).AddNormalPondTmp), arg0, arg1)
}

// BatchGetMagicSpiritLimitChannelTypeList mocks base method.
func (m *MockIStore) BatchGetMagicSpiritLimitChannelTypeList(arg0 context.Context, arg1 []uint32) (map[uint32][]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetMagicSpiritLimitChannelTypeList", arg0, arg1)
	ret0, _ := ret[0].(map[uint32][]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetMagicSpiritLimitChannelTypeList indicates an expected call of BatchGetMagicSpiritLimitChannelTypeList.
func (mr *MockIStoreMockRecorder) BatchGetMagicSpiritLimitChannelTypeList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetMagicSpiritLimitChannelTypeList", reflect.TypeOf((*MockIStore)(nil).BatchGetMagicSpiritLimitChannelTypeList), arg0, arg1)
}

// CreateMagicSpiritOrder mocks base method.
func (m *MockIStore) CreateMagicSpiritOrder(arg0 *gorm.DB, arg1 *mysql.MagicSpiritOrder) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateMagicSpiritOrder", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateMagicSpiritOrder indicates an expected call of CreateMagicSpiritOrder.
func (mr *MockIStoreMockRecorder) CreateMagicSpiritOrder(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateMagicSpiritOrder", reflect.TypeOf((*MockIStore)(nil).CreateMagicSpiritOrder), arg0, arg1)
}

// CreatePondTmpTable mocks base method.
func (m *MockIStore) CreatePondTmpTable() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreatePondTmpTable")
	ret0, _ := ret[0].(error)
	return ret0
}

// CreatePondTmpTable indicates an expected call of CreatePondTmpTable.
func (mr *MockIStoreMockRecorder) CreatePondTmpTable() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePondTmpTable", reflect.TypeOf((*MockIStore)(nil).CreatePondTmpTable))
}

// CreateSpecialPondTable mocks base method.
func (m *MockIStore) CreateSpecialPondTable() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateSpecialPondTable")
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateSpecialPondTable indicates an expected call of CreateSpecialPondTable.
func (mr *MockIStoreMockRecorder) CreateSpecialPondTable() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateSpecialPondTable", reflect.TypeOf((*MockIStore)(nil).CreateSpecialPondTable))
}

// CreateTable mocks base method.
func (m *MockIStore) CreateTable() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "CreateTable")
}

// CreateTable indicates an expected call of CreateTable.
func (mr *MockIStoreMockRecorder) CreateTable() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTable", reflect.TypeOf((*MockIStore)(nil).CreateTable))
}

// DelBlacklist mocks base method.
func (m *MockIStore) DelBlacklist(arg0 context.Context, arg1 []uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelBlacklist", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelBlacklist indicates an expected call of DelBlacklist.
func (mr *MockIStoreMockRecorder) DelBlacklist(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelBlacklist", reflect.TypeOf((*MockIStore)(nil).DelBlacklist), arg0, arg1)
}

// DelMagicSpirit mocks base method.
func (m *MockIStore) DelMagicSpirit(arg0 context.Context, arg1 []uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelMagicSpirit", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelMagicSpirit indicates an expected call of DelMagicSpirit.
func (mr *MockIStoreMockRecorder) DelMagicSpirit(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelMagicSpirit", reflect.TypeOf((*MockIStore)(nil).DelMagicSpirit), arg0, arg1)
}

// DelMagicSpiritPond mocks base method.
func (m *MockIStore) DelMagicSpiritPond(arg0 context.Context, arg1 uint32, arg2 []uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelMagicSpiritPond", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelMagicSpiritPond indicates an expected call of DelMagicSpiritPond.
func (mr *MockIStoreMockRecorder) DelMagicSpiritPond(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelMagicSpiritPond", reflect.TypeOf((*MockIStore)(nil).DelMagicSpiritPond), arg0, arg1, arg2)
}

// DelMagicSpiritTmp mocks base method.
func (m *MockIStore) DelMagicSpiritTmp(arg0 context.Context, arg1 []uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelMagicSpiritTmp", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelMagicSpiritTmp indicates an expected call of DelMagicSpiritTmp.
func (mr *MockIStoreMockRecorder) DelMagicSpiritTmp(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelMagicSpiritTmp", reflect.TypeOf((*MockIStore)(nil).DelMagicSpiritTmp), arg0, arg1)
}

// GetAwardCntSumById mocks base method.
func (m *MockIStore) GetAwardCntSumById(arg0 []uint32, arg1, arg2 time.Time) (*mysql.SumInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAwardCntSumById", arg0, arg1, arg2)
	ret0, _ := ret[0].(*mysql.SumInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAwardCntSumById indicates an expected call of GetAwardCntSumById.
func (mr *MockIStoreMockRecorder) GetAwardCntSumById(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAwardCntSumById", reflect.TypeOf((*MockIStore)(nil).GetAwardCntSumById), arg0, arg1, arg2)
}

// GetAwardStatistics mocks base method.
func (m *MockIStore) GetAwardStatistics(arg0, arg1 time.Time) (*mysql.Statistics, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAwardStatistics", arg0, arg1)
	ret0, _ := ret[0].(*mysql.Statistics)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAwardStatistics indicates an expected call of GetAwardStatistics.
func (mr *MockIStoreMockRecorder) GetAwardStatistics(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAwardStatistics", reflect.TypeOf((*MockIStore)(nil).GetAwardStatistics), arg0, arg1)
}

// GetAwardTotalInfo mocks base method.
func (m *MockIStore) GetAwardTotalInfo(arg0, arg1, arg2 time.Time) (*mysql.TotalInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAwardTotalInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(*mysql.TotalInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAwardTotalInfo indicates an expected call of GetAwardTotalInfo.
func (mr *MockIStoreMockRecorder) GetAwardTotalInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAwardTotalInfo", reflect.TypeOf((*MockIStore)(nil).GetAwardTotalInfo), arg0, arg1, arg2)
}

// GetBlacklist mocks base method.
func (m *MockIStore) GetBlacklist(arg0 context.Context, arg1, arg2, arg3 uint32) (uint32, []*mysql.MagicSpiritBlacklist, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBlacklist", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].([]*mysql.MagicSpiritBlacklist)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetBlacklist indicates an expected call of GetBlacklist.
func (mr *MockIStoreMockRecorder) GetBlacklist(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBlacklist", reflect.TypeOf((*MockIStore)(nil).GetBlacklist), arg0, arg1, arg2, arg3)
}

// GetBlacklistCIds mocks base method.
func (m *MockIStore) GetBlacklistCIds(arg0 context.Context) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBlacklistCIds", arg0)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBlacklistCIds indicates an expected call of GetBlacklistCIds.
func (mr *MockIStoreMockRecorder) GetBlacklistCIds(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBlacklistCIds", reflect.TypeOf((*MockIStore)(nil).GetBlacklistCIds), arg0)
}

// GetChannelOrderListByStatus mocks base method.
func (m *MockIStore) GetChannelOrderListByStatus(arg0 *gorm.DB, arg1, arg2 uint32, arg3 time.Time) ([]*mysql.MagicSpiritOrder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelOrderListByStatus", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*mysql.MagicSpiritOrder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelOrderListByStatus indicates an expected call of GetChannelOrderListByStatus.
func (mr *MockIStoreMockRecorder) GetChannelOrderListByStatus(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelOrderListByStatus", reflect.TypeOf((*MockIStore)(nil).GetChannelOrderListByStatus), arg0, arg1, arg2, arg3)
}

// GetCommonConf mocks base method.
func (m *MockIStore) GetCommonConf(arg0 context.Context) ([]*mysql.MagicSpiritCommonConf, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCommonConf", arg0)
	ret0, _ := ret[0].([]*mysql.MagicSpiritCommonConf)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCommonConf indicates an expected call of GetCommonConf.
func (mr *MockIStoreMockRecorder) GetCommonConf(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCommonConf", reflect.TypeOf((*MockIStore)(nil).GetCommonConf), arg0)
}

// GetEffectivePondTmp mocks base method.
func (m *MockIStore) GetEffectivePondTmp(arg0 context.Context) ([]*mysql.MagicSpiritPondTmp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEffectivePondTmp", arg0)
	ret0, _ := ret[0].([]*mysql.MagicSpiritPondTmp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEffectivePondTmp indicates an expected call of GetEffectivePondTmp.
func (mr *MockIStoreMockRecorder) GetEffectivePondTmp(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEffectivePondTmp", reflect.TypeOf((*MockIStore)(nil).GetEffectivePondTmp), arg0)
}

// GetMagicAwardOrderIdList mocks base method.
func (m *MockIStore) GetMagicAwardOrderIdList(arg0, arg1, arg2 time.Time) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMagicAwardOrderIdList", arg0, arg1, arg2)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMagicAwardOrderIdList indicates an expected call of GetMagicAwardOrderIdList.
func (mr *MockIStoreMockRecorder) GetMagicAwardOrderIdList(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMagicAwardOrderIdList", reflect.TypeOf((*MockIStore)(nil).GetMagicAwardOrderIdList), arg0, arg1, arg2)
}

// GetMagicConsumeOrderIdList mocks base method.
func (m *MockIStore) GetMagicConsumeOrderIdList(arg0, arg1, arg2 time.Time, arg3, arg4 uint32) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMagicConsumeOrderIdList", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMagicConsumeOrderIdList indicates an expected call of GetMagicConsumeOrderIdList.
func (mr *MockIStoreMockRecorder) GetMagicConsumeOrderIdList(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMagicConsumeOrderIdList", reflect.TypeOf((*MockIStore)(nil).GetMagicConsumeOrderIdList), arg0, arg1, arg2, arg3, arg4)
}

// GetMagicSpirit mocks base method.
func (m *MockIStore) GetMagicSpirit(arg0 context.Context) ([]*mysql.MagicSpirit, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMagicSpirit", arg0)
	ret0, _ := ret[0].([]*mysql.MagicSpirit)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMagicSpirit indicates an expected call of GetMagicSpirit.
func (mr *MockIStoreMockRecorder) GetMagicSpirit(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMagicSpirit", reflect.TypeOf((*MockIStore)(nil).GetMagicSpirit), arg0)
}

// GetMagicSpiritAwardLog mocks base method.
func (m *MockIStore) GetMagicSpiritAwardLog(arg0 *gorm.DB, arg1 string, arg2 time.Time) (*mysql.MagicSpiritAwardLog, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMagicSpiritAwardLog", arg0, arg1, arg2)
	ret0, _ := ret[0].(*mysql.MagicSpiritAwardLog)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetMagicSpiritAwardLog indicates an expected call of GetMagicSpiritAwardLog.
func (mr *MockIStoreMockRecorder) GetMagicSpiritAwardLog(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMagicSpiritAwardLog", reflect.TypeOf((*MockIStore)(nil).GetMagicSpiritAwardLog), arg0, arg1, arg2)
}

// GetMagicSpiritAwardLogs mocks base method.
func (m *MockIStore) GetMagicSpiritAwardLogs(arg0 *gorm.DB, arg1, arg2 time.Time, arg3 uint32) ([]*mysql.MagicSpiritAwardLog, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMagicSpiritAwardLogs", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*mysql.MagicSpiritAwardLog)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMagicSpiritAwardLogs indicates an expected call of GetMagicSpiritAwardLogs.
func (mr *MockIStoreMockRecorder) GetMagicSpiritAwardLogs(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMagicSpiritAwardLogs", reflect.TypeOf((*MockIStore)(nil).GetMagicSpiritAwardLogs), arg0, arg1, arg2, arg3)
}

// GetMagicSpiritById mocks base method.
func (m *MockIStore) GetMagicSpiritById(arg0 context.Context, arg1 uint32) (*mysql.MagicSpirit, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMagicSpiritById", arg0, arg1)
	ret0, _ := ret[0].(*mysql.MagicSpirit)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMagicSpiritById indicates an expected call of GetMagicSpiritById.
func (mr *MockIStoreMockRecorder) GetMagicSpiritById(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMagicSpiritById", reflect.TypeOf((*MockIStore)(nil).GetMagicSpiritById), arg0, arg1)
}

// GetMagicSpiritByIds mocks base method.
func (m *MockIStore) GetMagicSpiritByIds(arg0 context.Context, arg1 []uint32) (map[uint32]*mysql.MagicSpirit, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMagicSpiritByIds", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]*mysql.MagicSpirit)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMagicSpiritByIds indicates an expected call of GetMagicSpiritByIds.
func (mr *MockIStoreMockRecorder) GetMagicSpiritByIds(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMagicSpiritByIds", reflect.TypeOf((*MockIStore)(nil).GetMagicSpiritByIds), arg0, arg1)
}

// GetMagicSpiritOrder mocks base method.
func (m *MockIStore) GetMagicSpiritOrder(arg0 *gorm.DB, arg1 string, arg2 time.Time) (*mysql.MagicSpiritOrder, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMagicSpiritOrder", arg0, arg1, arg2)
	ret0, _ := ret[0].(*mysql.MagicSpiritOrder)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetMagicSpiritOrder indicates an expected call of GetMagicSpiritOrder.
func (mr *MockIStoreMockRecorder) GetMagicSpiritOrder(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMagicSpiritOrder", reflect.TypeOf((*MockIStore)(nil).GetMagicSpiritOrder), arg0, arg1, arg2)
}

// GetMagicSpiritPond mocks base method.
func (m *MockIStore) GetMagicSpiritPond(arg0 context.Context, arg1 uint32) ([]*mysql.MagicSpiritPond, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMagicSpiritPond", arg0, arg1)
	ret0, _ := ret[0].([]*mysql.MagicSpiritPond)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMagicSpiritPond indicates an expected call of GetMagicSpiritPond.
func (mr *MockIStoreMockRecorder) GetMagicSpiritPond(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMagicSpiritPond", reflect.TypeOf((*MockIStore)(nil).GetMagicSpiritPond), arg0, arg1)
}

// GetMagicSpiritTmp mocks base method.
func (m *MockIStore) GetMagicSpiritTmp(arg0 context.Context, arg1 time.Time) ([]*mysql.MagicSpiritTemporary, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMagicSpiritTmp", arg0, arg1)
	ret0, _ := ret[0].([]*mysql.MagicSpiritTemporary)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMagicSpiritTmp indicates an expected call of GetMagicSpiritTmp.
func (mr *MockIStoreMockRecorder) GetMagicSpiritTmp(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMagicSpiritTmp", reflect.TypeOf((*MockIStore)(nil).GetMagicSpiritTmp), arg0, arg1)
}

// GetMagicSpiritTmpEffective mocks base method.
func (m *MockIStore) GetMagicSpiritTmpEffective(arg0 context.Context, arg1 time.Time) ([]*mysql.MagicSpiritTemporary, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMagicSpiritTmpEffective", arg0, arg1)
	ret0, _ := ret[0].([]*mysql.MagicSpiritTemporary)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMagicSpiritTmpEffective indicates an expected call of GetMagicSpiritTmpEffective.
func (mr *MockIStoreMockRecorder) GetMagicSpiritTmpEffective(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMagicSpiritTmpEffective", reflect.TypeOf((*MockIStore)(nil).GetMagicSpiritTmpEffective), arg0, arg1)
}

// GetNormalPondMaxUpdateTime mocks base method.
func (m *MockIStore) GetNormalPondMaxUpdateTime(arg0 context.Context) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNormalPondMaxUpdateTime", arg0)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNormalPondMaxUpdateTime indicates an expected call of GetNormalPondMaxUpdateTime.
func (mr *MockIStoreMockRecorder) GetNormalPondMaxUpdateTime(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNormalPondMaxUpdateTime", reflect.TypeOf((*MockIStore)(nil).GetNormalPondMaxUpdateTime), arg0)
}

// GetNormalPondTmp mocks base method.
func (m *MockIStore) GetNormalPondTmp(arg0 context.Context) ([]*mysql.MagicSpiritPondTmp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNormalPondTmp", arg0)
	ret0, _ := ret[0].([]*mysql.MagicSpiritPondTmp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNormalPondTmp indicates an expected call of GetNormalPondTmp.
func (mr *MockIStoreMockRecorder) GetNormalPondTmp(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNormalPondTmp", reflect.TypeOf((*MockIStore)(nil).GetNormalPondTmp), arg0)
}

// GetOrderTotalInfo mocks base method.
func (m *MockIStore) GetOrderTotalInfo(arg0, arg1, arg2 time.Time, arg3, arg4 uint32) (*mysql.TotalInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrderTotalInfo", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*mysql.TotalInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrderTotalInfo indicates an expected call of GetOrderTotalInfo.
func (mr *MockIStoreMockRecorder) GetOrderTotalInfo(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrderTotalInfo", reflect.TypeOf((*MockIStore)(nil).GetOrderTotalInfo), arg0, arg1, arg2, arg3, arg4)
}

// GetReconcileSumStats mocks base method.
func (m *MockIStore) GetReconcileSumStats(arg0, arg1 time.Time) ([]*mysql.ReconcileDataLog, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetReconcileSumStats", arg0, arg1)
	ret0, _ := ret[0].([]*mysql.ReconcileDataLog)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetReconcileSumStats indicates an expected call of GetReconcileSumStats.
func (mr *MockIStoreMockRecorder) GetReconcileSumStats(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReconcileSumStats", reflect.TypeOf((*MockIStore)(nil).GetReconcileSumStats), arg0, arg1)
}

// GetSpecialPondByMagicId mocks base method.
func (m *MockIStore) GetSpecialPondByMagicId(arg0 context.Context, arg1 uint32) ([]*mysql.MagicSpiritPond, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSpecialPondByMagicId", arg0, arg1)
	ret0, _ := ret[0].([]*mysql.MagicSpiritPond)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSpecialPondByMagicId indicates an expected call of GetSpecialPondByMagicId.
func (mr *MockIStoreMockRecorder) GetSpecialPondByMagicId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSpecialPondByMagicId", reflect.TypeOf((*MockIStore)(nil).GetSpecialPondByMagicId), arg0, arg1)
}

// GetSpecialPondMaxUpdateTime mocks base method.
func (m *MockIStore) GetSpecialPondMaxUpdateTime(arg0 context.Context) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSpecialPondMaxUpdateTime", arg0)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSpecialPondMaxUpdateTime indicates an expected call of GetSpecialPondMaxUpdateTime.
func (mr *MockIStoreMockRecorder) GetSpecialPondMaxUpdateTime(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSpecialPondMaxUpdateTime", reflect.TypeOf((*MockIStore)(nil).GetSpecialPondMaxUpdateTime), arg0)
}

// RecordMagicSpiritAwardLog mocks base method.
func (m *MockIStore) RecordMagicSpiritAwardLog(arg0 *gorm.DB, arg1 *mysql.MagicSpiritAwardLog) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecordMagicSpiritAwardLog", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecordMagicSpiritAwardLog indicates an expected call of RecordMagicSpiritAwardLog.
func (mr *MockIStoreMockRecorder) RecordMagicSpiritAwardLog(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordMagicSpiritAwardLog", reflect.TypeOf((*MockIStore)(nil).RecordMagicSpiritAwardLog), arg0, arg1)
}

// RecordMagicSpiritAwardLogs mocks base method.
func (m *MockIStore) RecordMagicSpiritAwardLogs(arg0 *gorm.DB, arg1 []*mysql.MagicSpiritAwardLog) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecordMagicSpiritAwardLogs", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecordMagicSpiritAwardLogs indicates an expected call of RecordMagicSpiritAwardLogs.
func (mr *MockIStoreMockRecorder) RecordMagicSpiritAwardLogs(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordMagicSpiritAwardLogs", reflect.TypeOf((*MockIStore)(nil).RecordMagicSpiritAwardLogs), arg0, arg1)
}

// RecordReconcileDataLogs mocks base method.
func (m *MockIStore) RecordReconcileDataLogs(arg0 []*mysql.ReconcileDataLog) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecordReconcileDataLogs", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecordReconcileDataLogs indicates an expected call of RecordReconcileDataLogs.
func (mr *MockIStoreMockRecorder) RecordReconcileDataLogs(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordReconcileDataLogs", reflect.TypeOf((*MockIStore)(nil).RecordReconcileDataLogs), arg0)
}

// SetCommonConf mocks base method.
func (m *MockIStore) SetCommonConf(arg0 context.Context, arg1 []*mysql.MagicSpiritCommonConf) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetCommonConf", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetCommonConf indicates an expected call of SetCommonConf.
func (mr *MockIStoreMockRecorder) SetCommonConf(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetCommonConf", reflect.TypeOf((*MockIStore)(nil).SetCommonConf), arg0, arg1)
}

// SetMagicSpiritUpdateFlag mocks base method.
func (m *MockIStore) SetMagicSpiritUpdateFlag(arg0 context.Context, arg1 *gorm.DB, arg2 uint32, arg3 time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetMagicSpiritUpdateFlag", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetMagicSpiritUpdateFlag indicates an expected call of SetMagicSpiritUpdateFlag.
func (mr *MockIStoreMockRecorder) SetMagicSpiritUpdateFlag(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetMagicSpiritUpdateFlag", reflect.TypeOf((*MockIStore)(nil).SetMagicSpiritUpdateFlag), arg0, arg1, arg2, arg3)
}

// Transaction mocks base method.
func (m *MockIStore) Transaction(arg0 context.Context, arg1 func(*gorm.DB) error) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Transaction", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Transaction indicates an expected call of Transaction.
func (mr *MockIStoreMockRecorder) Transaction(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Transaction", reflect.TypeOf((*MockIStore)(nil).Transaction), arg0, arg1)
}

// UpdateMagicSpirit mocks base method.
func (m *MockIStore) UpdateMagicSpirit(arg0 context.Context, arg1 *gorm.DB, arg2 *mysql.MagicSpirit) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateMagicSpirit", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateMagicSpirit indicates an expected call of UpdateMagicSpirit.
func (mr *MockIStoreMockRecorder) UpdateMagicSpirit(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateMagicSpirit", reflect.TypeOf((*MockIStore)(nil).UpdateMagicSpirit), arg0, arg1, arg2)
}

// UpdateMagicSpiritAwardDone mocks base method.
func (m *MockIStore) UpdateMagicSpiritAwardDone(arg0 *gorm.DB, arg1 string, arg2, arg3 time.Time) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateMagicSpiritAwardDone", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateMagicSpiritAwardDone indicates an expected call of UpdateMagicSpiritAwardDone.
func (mr *MockIStoreMockRecorder) UpdateMagicSpiritAwardDone(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateMagicSpiritAwardDone", reflect.TypeOf((*MockIStore)(nil).UpdateMagicSpiritAwardDone), arg0, arg1, arg2, arg3)
}

// UpdateMagicSpiritAwardTBeanTime mocks base method.
func (m *MockIStore) UpdateMagicSpiritAwardTBeanTime(arg0 *gorm.DB, arg1, arg2, arg3 string, arg4 time.Time) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateMagicSpiritAwardTBeanTime", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateMagicSpiritAwardTBeanTime indicates an expected call of UpdateMagicSpiritAwardTBeanTime.
func (mr *MockIStoreMockRecorder) UpdateMagicSpiritAwardTBeanTime(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateMagicSpiritAwardTBeanTime", reflect.TypeOf((*MockIStore)(nil).UpdateMagicSpiritAwardTBeanTime), arg0, arg1, arg2, arg3, arg4)
}

// UpdateMagicSpiritOrderStatus mocks base method.
func (m *MockIStore) UpdateMagicSpiritOrderStatus(arg0 *gorm.DB, arg1 string, arg2 time.Time, arg3 []uint32, arg4 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateMagicSpiritOrderStatus", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateMagicSpiritOrderStatus indicates an expected call of UpdateMagicSpiritOrderStatus.
func (mr *MockIStoreMockRecorder) UpdateMagicSpiritOrderStatus(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateMagicSpiritOrderStatus", reflect.TypeOf((*MockIStore)(nil).UpdateMagicSpiritOrderStatus), arg0, arg1, arg2, arg3, arg4)
}

// UpdateMagicSpiritOrderTBeanTime mocks base method.
func (m *MockIStore) UpdateMagicSpiritOrderTBeanTime(arg0 *gorm.DB, arg1, arg2 string, arg3 time.Time) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateMagicSpiritOrderTBeanTime", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateMagicSpiritOrderTBeanTime indicates an expected call of UpdateMagicSpiritOrderTBeanTime.
func (mr *MockIStoreMockRecorder) UpdateMagicSpiritOrderTBeanTime(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateMagicSpiritOrderTBeanTime", reflect.TypeOf((*MockIStore)(nil).UpdateMagicSpiritOrderTBeanTime), arg0, arg1, arg2, arg3)
}

// UpdatePondTmpFlag mocks base method.
func (m *MockIStore) UpdatePondTmpFlag(arg0 context.Context, arg1 *gorm.DB, arg2 []uint32) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePondTmpFlag", arg0, arg1, arg2)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdatePondTmpFlag indicates an expected call of UpdatePondTmpFlag.
func (mr *MockIStoreMockRecorder) UpdatePondTmpFlag(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePondTmpFlag", reflect.TypeOf((*MockIStore)(nil).UpdatePondTmpFlag), arg0, arg1, arg2)
}

// UpdateSpecialPond mocks base method.
func (m *MockIStore) UpdateSpecialPond(arg0 context.Context, arg1 uint32, arg2 []*mysql.MagicSpiritPond) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateSpecialPond", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateSpecialPond indicates an expected call of UpdateSpecialPond.
func (mr *MockIStoreMockRecorder) UpdateSpecialPond(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSpecialPond", reflect.TypeOf((*MockIStore)(nil).UpdateSpecialPond), arg0, arg1, arg2)
}
