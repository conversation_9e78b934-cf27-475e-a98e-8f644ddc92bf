package internal

import (
    "crypto/aes"
    "crypto/cipher"
    "encoding/base64"
    "golang.52tt.com/pkg/log"
    "context"
    "encoding/json"
    "fmt"
    "crypto/md5"
    "golang.52tt.com/pkg/device_id"
    grpc "gitlab.ttyuyin.com/avengers/tyr/core/service/basepb/info"
)

var key = []byte("e1WbX55dmzJsVzgW")

func getInfoByAESDecrypt(ctx context.Context, encryptedStr string) ([]byte, error) {
    cipherText, err := base64.StdEncoding.DecodeString(encryptedStr)
    if err != nil {
        log.ErrorWithCtx(ctx, "getInfoByAESDecrypt fail")
        return nil, fmt.Errorf("参数decode失败")
    }

    if string(cipherText) == "" {
        return nil, fmt.Errorf("cipherText is empty")
    }
    org := AESDecrypt(cipherText, key)
    return org, nil
}

func GetClientInfoByEncodedStr(ctx context.Context, uid uint32, clientInfoStr string) (*ClientInfo, error) {
    org, err := getInfoByAESDecrypt(ctx, clientInfoStr)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetClientInfoByEncodedStr fail")
        return nil, fmt.Errorf("参数解析失败")
    }
    ci := &ClientInfo{}
    err = json.Unmarshal(org, &ci)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetClientInfoByEncodedStr fail")
        return ci, fmt.Errorf("参数解析失败")
    }

    // 签名校验
    if !ci.clientInfoSignCheck(ctx, uid) {
        log.ErrorWithCtx(ctx, "GetClientInfoByEncodedStr sign check fail")
        return ci, fmt.Errorf("参数校验失败")
    }

    return ci, nil
}

func (ci *ClientInfo) clientInfoSignCheck(ctx context.Context, uid uint32) bool {
    // 拼接字符串
    signStr := fmt.Sprintf("%s&%d&%d&%d&%d", ci.DeviceId, ci.ClientType, ci.MarketId, ci.AppVersion, uid)
    // 计算MD5
    hash := md5.Sum([]byte(signStr))

    // Base64编码
    base64Str := base64.StdEncoding.EncodeToString(hash[:])

    log.InfoWithCtx(ctx, "GetClientInfoByEncodedStr sign check success, sign:%s", base64Str)
    if base64Str != ci.Sign {
        log.ErrorWithCtx(ctx, "GetClientInfoByEncodedStr sign check fail")
        return false
    }

    return true
}

// UnPadding 去掉填充
func UnPadding(org []byte) []byte {
    l := len(org)
    if l <= 0 {
        return org
    }
    pad := int(org[l-1])
    if l < pad {
        return org
    }
    return org[:l-pad]
}

// AESDecrypt AES解密
func AESDecrypt(cipherTxt []byte, key []byte) []byte {
    block, _ := aes.NewCipher(key)
    blockMode := cipher.NewCBCDecrypter(block, key)
    org := make([]byte, len(cipherTxt))
    blockMode.CryptBlocks(org, cipherTxt)
    org = UnPadding(org)
    return org
}

/*
// AESEncrypt AES加密
func AESEncrypt(org []byte, key []byte) []byte {
    block, _ := aes.NewCipher(key)
    org = Padding(org, block.BlockSize())
    blockMode := cipher.NewCBCEncrypter(block, key)
    cryted := make([]byte, len(org))
    blockMode.CryptBlocks(cryted, org)
    return cryted
}

// Padding 填充16倍数
func Padding(org []byte, blockSize int) []byte {
    pad := blockSize - len(org)%blockSize
    padArr := bytes.Repeat([]byte{byte(pad)}, pad)
    return append(org, padArr...)
}
*/
func genServiceInfoCtx(clientInfo *ClientInfo, uid uint32) *grpc.ServiceInfo {
    serviceInfo := &grpc.ServiceInfo{}
    serviceInfo.UserID = uid
    serviceInfo.DeviceID = device_id.ParseStringDeviceId(clientInfo.DeviceId)
    serviceInfo.ClientVersion = clientInfo.AppVersion
    serviceInfo.ClientType = uint16(clientInfo.ClientType)
    serviceInfo.MarketID = clientInfo.MarketId
    log.Debugf("genServiceInfoCtx serviceInfo:%+v", serviceInfo)
    return serviceInfo
}
