package main

import (
    "bytes"
    "context"
    "encoding/base64"
    "encoding/json"
    "fmt"
    mysqlConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/mysql/connect"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    virtual_image_user "golang.52tt.com/protocol/services/virtual-image-user"
    "golang.52tt.com/services/tt-rev/common/feishu"
    "io"
    "io/ioutil"
    "mime/multipart"
    "net/http"
    "os"
    "path/filepath"
    "strings"
    "time"

    "github.com/disintegration/imaging"
    "github.com/h2non/filetype"
)

var (
    cfg       *Config
    st        *Store
    viUserCli virtual_image_user.VirtualImageUserClient
)

const (
    FeishuAPI     = "https://open.feishu.cn/open-apis/im/v1/images"
    ImageMaxSize  = 10 << 20 // 10MB
    UploadTimeout = 30 * time.Second
)

type Config struct {
    FeiShuWebhook string                    `json:"feishu_webhook"`
    AppID         string                    `json:"app_id"`
    AppSecret     string                    `json:"app_secret"`
    MysqlConfig   *mysqlConnect.MysqlConfig `json:"mysql"`
    CheckSec      uint32                    `json:"check_sec"`
}

func (sc *Config) Parse(configFile string) (err error) {
    defer func() {
        if e := recover(); e != nil {
            err = fmt.Errorf("Failed to parse config: %v \n", e)
        }
    }()

    data, err := ioutil.ReadFile(configFile)
    if err != nil {
        return err
    }
    err = json.Unmarshal(data, &sc)
    if err != nil {
        return err
    }

    fmt.Printf("ServiceConfigT:MysqlConfig:%+v\n", sc.MysqlConfig)
    return
}

// 获取飞书访问令牌
func getFeishuToken() (string, error) {
    url := "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal"
    payload := map[string]string{
        "app_id":     cfg.AppID,
        "app_secret": cfg.AppSecret,
    }

    resp, err := postJSON(url, payload)
    if err != nil {
        return "", fmt.Errorf("获取token失败: %v", err)
    }

    var result struct {
        Code  int    `json:"code"`
        Msg   string `json:"msg"`
        Token string `json:"tenant_access_token"`
    }

    if err := json.Unmarshal(resp, &result); err != nil {
        return "", fmt.Errorf("解析token响应失败: %v", err)
    }

    if result.Code != 0 {
        return "", fmt.Errorf("飞书API错误: %s", result.Msg)
    }

    return result.Token, nil
}

// 修复并转换图片
func repairImage(inputPath string) ([]byte, error) {
    // 读取原始文件
    data, err := os.ReadFile(inputPath)
    if err != nil {
        return nil, fmt.Errorf("读取文件失败: %v", err)
    }

    // 验证图片类型
    if !filetype.IsImage(data) {
        return nil, fmt.Errorf("非图片文件类型")
    }

    // 解码图片（自动修复常见错误）
    img, err := imaging.Decode(bytes.NewReader(data), imaging.AutoOrientation(true))
    if err != nil {
        return nil, fmt.Errorf("图片解码失败: %v", err)
    }

    // 转换为JPEG格式
    var buf bytes.Buffer
    if err := imaging.Encode(&buf, img, imaging.JPEG, imaging.JPEGQuality(85)); err != nil {
        return nil, fmt.Errorf("图片编码失败: %v", err)
    }

    return buf.Bytes(), nil
}

// 确保multipart表单格式正确
func createUploadBody(imageData []byte) (io.Reader, string, error) {
    body := &bytes.Buffer{}
    writer := multipart.NewWriter(body)

    part, err := writer.CreateFormFile("image", "upload.jpg") // 必须提供文件名
    if err != nil {
        return nil, "", err
    }
    if _, err := io.Copy(part, bytes.NewReader(imageData)); err != nil {
        return nil, "", err
    }

    // 必须包含image_type字段
    if err := writer.WriteField("image_type", "message"); err != nil {
        return nil, "", err
    }

    contentType := writer.FormDataContentType()
    writer.Close()

    return body, contentType, nil
}

// 上传图片到飞书
func uploadToFeishu(imageData []byte, token string) (string, error) {
    // 1. 创建请求体
    body, contentType, err := createUploadBody(imageData)
    if err != nil {
        return "", fmt.Errorf("创建请求体失败: %v", err)
    }

    // 2. 创建HTTP请求
    req, err := http.NewRequest("POST", FeishuAPI, body)
    if err != nil {
        return "", fmt.Errorf("创建请求失败: %v", err)
    }

    // 3. 设置必须的Header
    req.Header.Set("Authorization", "Bearer "+token)
    req.Header.Set("Content-Type", contentType)
    req.Header.Set("Accept", "application/json")

    // 4. 发送请求
    client := &http.Client{
        Timeout: UploadTimeout,
        // 建议添加重定向策略
        CheckRedirect: func(req *http.Request, via []*http.Request) error {
            return http.ErrUseLastResponse
        },
    }

    fmt.Printf("请求request: %+v\n", req.Header)

    resp, err := client.Do(req)
    if err != nil {
        return "", fmt.Errorf("请求发送失败: %v", err)
    }
    defer resp.Body.Close()

    // 5. 处理响应
    if resp.StatusCode != http.StatusOK {
        respBody, _ := io.ReadAll(resp.Body)
        return "", fmt.Errorf("HTTP %d 错误: %s", resp.StatusCode, string(respBody))
    }

    var result struct {
        Code int    `json:"code"`
        Msg  string `json:"msg"`
        Data struct {
            ImageKey string `json:"image_key"`
        } `json:"data"`
    }

    if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
        return "", fmt.Errorf("响应解析失败: %v", err)
    }

    if result.Code != 0 {
        return "", fmt.Errorf("飞书API错误[%d]: %s", result.Code, result.Msg)
    }

    return result.Data.ImageKey, nil
}

// 从URL下载图片
func downloadImage(url string) ([]byte, error) {
    resp, err := http.Get(url)
    if err != nil {
        return nil, fmt.Errorf("下载失败: %v", err)
    }
    defer resp.Body.Close()

    if resp.StatusCode != http.StatusOK {
        return nil, fmt.Errorf("HTTP错误: %s", resp.Status)
    }

    // 限制读取大小
    limitedReader := &io.LimitedReader{R: resp.Body, N: ImageMaxSize}
    data, err := io.ReadAll(limitedReader)
    if err != nil {
        return nil, fmt.Errorf("读取数据失败: %v", err)
    }

    if limitedReader.N <= 0 {
        return nil, fmt.Errorf("文件超过大小限制(10MB)")
    }

    return data, nil
}

// 处理单张图片
func processImage(url, token string) (string, error) {
    // 1. 下载图片
    data, err := downloadImage(url)
    if err != nil {
        return "", fmt.Errorf("下载失败: %v", err)
    }

    // 2. 保存临时文件
    tmpFile := filepath.Join(os.TempDir(), fmt.Sprintf("upload_%d.jpg", time.Now().UnixNano()))
    if err := os.WriteFile(tmpFile, data, 0600); err != nil {
        return "", fmt.Errorf("保存临时文件失败: %v", err)
    }
    defer os.Remove(tmpFile)

    // 3. 修复图片
    repaired, err := repairImage(tmpFile)
    if err != nil {
        return "", fmt.Errorf("图片修复失败: %v", err)
    }

    // 在使用前调用
    if err := validateImage(repaired); err != nil {
        return "", fmt.Errorf("图片验证失败: %v", err)
    }

    // 4. 上传到飞书
    imageKey, err := uploadToFeishu(repaired, token)
    if err != nil {
        return "", fmt.Errorf("上传失败: %v", err)
    }

    return imageKey, nil
}

func postJSON(url string, data interface{}) ([]byte, error) {
    jsonData, err := json.Marshal(data)
    if err != nil {
        return nil, err
    }

    resp, err := http.Post(url, "application/json", bytes.NewBuffer(jsonData))
    if err != nil {
        return nil, err
    }
    defer resp.Body.Close()

    return io.ReadAll(resp.Body)
}

// 在修复图片后添加验证
func validateImage(data []byte) error {
    // 检查最小文件大小（JPEG文件头至少2字节）
    if len(data) < 2 {
        return fmt.Errorf("文件过小")
    }

    // 检查JPEG文件头
    if !bytes.HasPrefix(data, []byte{0xFF, 0xD8}) {
        return fmt.Errorf("无效的JPEG文件头")
    }

    // 检查文件大小限制（飞书要求≤10MB）
    if len(data) > 10<<20 {
        return fmt.Errorf("文件超过10MB限制")
    }

    return nil
}

// sendFeiShuMsg 发送飞书消息
func sendFeiShuMsg(title string, textLines []string, picKey string) error {
    lineList := make([][]*feishu.LineMem, 0, len(textLines))
    for _, text := range textLines {
        line := []*feishu.LineMem{
            {Tag: "text", Text: text},
        }
        lineList = append(lineList, line)
    }

    if picKey != "" {
        lineList = append(lineList, []*feishu.LineMem{
            {Tag: "img", ImageKey: picKey},
        })
    }

    return feishu.SendFeiShuRichMsg(cfg.FeiShuWebhook, title, lineList)
}

func main() {
    if len(os.Args) < 2 {
        fmt.Printf("Usage: %s configFile.", os.Args[0])
        return
    }

    now := time.Now()
    cfg = &Config{}
    err := cfg.Parse(os.Args[1])
    if err != nil {
        log.Errorf("Parse fail. err:%v", err)
        return
    }

    ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
    defer cancel()

    st, err = NewStore(ctx)
    if err != nil {
        log.Errorf("NewStore fail. err:%v", err)
        return
    }
    defer st.Close()

    e := now
    b := now.Add(-time.Duration(int64(cfg.CheckSec)) * time.Second)

    fmt.Printf("开始时间: %s, 结束时间: %s\n", b.Format("2006-01-02 15:04:05"), e.Format("2006-01-02 15:04:05"))

    viUserCli = virtual_image_user.MustNewClient(ctx)

    records, err := st.GetSceneClipRecordListByTime(ctx, b, e)
    if err != nil {
        log.Errorf("GetSceneClipRecordListByTime fail. err:%v", err)
        return
    }

    if len(records) == 0 {
        log.Infof("没有符合条件的记录")
        return
    }

    uidList := make([]uint32, 0, len(records)*2)
    for _, r := range records {
        uidList = append(uidList, r.BrideUid, r.GroomUid)
    }

    // 获取穿戴信息
    inuseItemResp, err := viUserCli.BatchGetUserInuseItemInfo(ctx, &virtual_image_user.BatchGetUserInuseItemInfoReq{
        UidList: uidList,
    })
    if err != nil {
        log.Errorf("BatchGetUserInuseItemInfo fail. err:%v", err)
        return
    }

    uid2InuseItems := make(map[uint32]*virtual_image_user.UserInuseItemInfo)
    for _, info := range inuseItemResp.GetUserInuseItemInfo() {
        uid2InuseItems[info.GetUid()] = info
    }

    // 获取飞书Token
    token, err := getFeishuToken()
    if err != nil {
        log.Fatalf("获取Token失败: %v", err)
    }
    fmt.Printf("获取Token成功: %s\n", token)

    // 处理
    for _, r := range records {
        url := r.ScenePic
        fmt.Printf("正在处理: %+v\n", r)
        imageKey, err := processImage(url, token)
        if err != nil {
            log.Errorf("处理失败: %v", err)
            continue
        }
        fmt.Printf("上传成功! Image Key: %s\n\n", imageKey)

        // 4. 发送飞书消息
        title, textLines := genTextLines(r, uid2InuseItems[r.BrideUid], uid2InuseItems[r.GroomUid])
        if err := sendFeiShuMsg(title, textLines, imageKey); err != nil {
            log.Errorf("发送消息失败: %v", err)
        } else {
            fmt.Println("消息发送成功!")
        }
    }
}

var sceneStrMap = map[uint32]string{
    1: "新人进场",
    2: "交换戒指",
    3: "高光时刻",
    4: "合影留恋",
}

func genTextLines(r *SceneClipRecord, brideInuse, groomInuse *virtual_image_user.UserInuseItemInfo) (string, []string) {
    brideItems, groomItems := make([]uint32, 0), make([]uint32, 0)
    for _, item := range brideInuse.GetItems() {
        brideItems = append(brideItems, item.GetCfgId())
    }
    for _, item := range groomInuse.GetItems() {
        groomItems = append(groomItems, item.GetCfgId())
    }

    title := fmt.Sprintf("婚礼ID: %d | CID: %d | %s", r.WeddingId, r.Cid, sceneStrMap[r.SceneType])
    return title, []string{
        fmt.Sprintf("新娘UID: %d | 新郎UID: %d", r.BrideUid, r.GroomUid),
        fmt.Sprintf("新娘穿戴物品列表: %v", brideItems),
        fmt.Sprintf("新郎穿戴物品列表: %v", groomItems),
        fmt.Sprintf("主题ID: %d | 主题名称: %s", r.ThemeId, r.ThemeName),
        fmt.Sprintf("场景id: %d | 场景时间: %s", r.Id, r.WeddingTime.Format("2006-01-02 15:04:05")),
        fmt.Sprintf("上报人uid: %d | 上报设备：%s", r.ReportUid, getPlatformInfo(r.ScenePic)),
        fmt.Sprintf("图片url: %s", r.ScenePic),
    }
}

// 根据url获取平台信息
func getPlatformInfo(url string) string {
    if strings.Contains(url, "wedding/pc-") {
        return "PC"
    }

    list := strings.Split(url, "/")
    if len(list) < 2 {
        return "未知"
    }

    str := list[len(list)-1]

    // base64解码
    decoded, err := base64.StdEncoding.DecodeString(str)
    if err != nil {
        return "IOS"
    }

    if strings.Contains(string(decoded), "scene_photo") {
        return "安卓"
    }

    return "IOS"
}
