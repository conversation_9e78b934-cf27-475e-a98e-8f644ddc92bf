package mysql

import (
	"errors"
	"fmt"
	"time"

	"github.com/go-sql-driver/mysql"
	"github.com/jinzhu/gorm"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
)

/*
CREATE TABLE `cancel_contract_apply_00` (
  `guild_id` int(10) unsigned NOT NULL,
  `guild_name` varchar(32) NOT NULL DEFAULT '',
  `uid` int(10) unsigned NOT NULL,
  `status` int(2) unsigned NOT NULL DEFAULT '0',
  `apply_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `cancel_type` tinyint NOT NULL DEFAULT 0 COMMENT '解约方式',
  `reason` tinyint(3) unsigned NOT NULL DEFAULT 0 COMMENT '解约原因',
  `reason_text` varchar(1024) NOT NULL DEFAULT '' COMMENT '解约原因文本',
  `image_proof_list` varchar(1024) NOT NULL DEFAULT '' COMMENT '图片凭证列表',
  `video_proof_list` varchar(1024) NOT NULL DEFAULT '' COMMENT '视频凭证列表',
  PRIMARY KEY (`uid`,`guild_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ;
*/

// 主播签约身份
type CancelContractApply struct {
	Uid                 uint32    `db:"uid"`
	GuildId             uint32    `db:"guild_id"`
	GuildName           string    `db:"guild_name"`
	Status              uint32    `db:"status"`
	ApplyTime           time.Time `db:"apply_time"`
	CancelType          uint32    `db:"cancel_type"`
	Reason              uint32    `db:"reason"`
	ReasonText          string    `db:"reason_text" gorm:"type:varchar(2048)"`
	ImageProofList      string    `db:"image_proof_list" gorm:"type:varchar(1024)"`
	VideoProofList      string    `db:"video_proof_list" gorm:"type:varchar(1024)"`
	NegotiateReasonType string    `db:"negotiate_reason_type" gorm:"type:varchar(1024)"`
}

func (t *CancelContractApply) TableName() string {
	return fmt.Sprintf("cancel_contract_apply_%02d", t.GuildId%100)
}

func (s *Store) AddCancelContractApply(tx *gorm.DB, info *CancelContractApply) error {
	db := s.db
	if tx != nil {
		db = tx
	}

	err := db.Create(info).Error
	if err != nil {
		if mysqlErr, ok := err.(*mysql.MySQLError); ok && mysqlErr.Number == 1062 {
			return protocol.NewExactServerError(nil, -1062)
		}

		log.Errorf("AddCancelContractApply fail to Create. info:%+v, err:%v", info, err)
		return err
	}

	log.Debugf("AddCancelContractApply info:%+v", info)
	return nil
}

func (s *Store) HandlerCancelContractApply(tx *gorm.DB, uid, guildId, status uint32) error {
	db := s.db
	if tx != nil {
		db = tx
	}

	err := db.Model(&CancelContractApply{GuildId: guildId}).Where("guild_id=? and uid = ? and status in(0,4,6)", guildId, uid).
		Update("status", status).Error
	if err != nil {
		log.Errorf("HandlerCancelContractApply fail to Create. uid:%+v, guildId:%+v, status:%+v, err:%v", uid, guildId, status, err)
		return err
	}

	log.Debugf("HandlerCancelContractApply uid:%+v, guildId:%+v, status:%+v", uid, guildId, status)
	return nil
}

func (s *Store) DelCancelContractApply(tx *gorm.DB, uid, guildId uint32) error {
	db := s.db
	if tx != nil {
		db = tx
	}

	err := db.Delete(&CancelContractApply{GuildId: guildId}, "uid=? and guild_id=?", uid, guildId).Error
	if err != nil {
		log.Errorf("DelCancelContractApply fail to Delete. uid:%+v, guildId:%+v, err:%v", uid, guildId, err)
		return err
	}

	log.Debugf("DelCancelContractApply uid:%+v, guildId:%+v", uid, guildId)
	return nil
}

func (s *Store) GetGuildCancelContractApplyList(guildId, begin, limit uint32) ([]*CancelContractApply, error) {
	list := make([]*CancelContractApply, 0, limit)

	err := s.db.Model(&CancelContractApply{GuildId: guildId}).
		Select("uid,guild_id,guild_name,status,apply_time, cancel_type, reason, reason_text, image_proof_list, video_proof_list , negotiate_reason_type").
		Where("guild_id=? and status in(0, 4, 6)", guildId).
		Order("apply_time DESC").Offset(begin).Limit(limit).
		Scan(&list).Error

	if err != nil {
		log.Errorf("GetGuildCancelContractApplyList fail to Select. guildId:%+v, err:%v", guildId, err)
		return list, err
	}

	log.Debugf("GetGuildCancelContractApplyList guildId:%+v, list:%v", guildId, list)
	return list, nil
}

func (s *Store) GetGuildCancelContractApplyCnt(guildId uint32, expiredTime time.Time) (uint32, error) {

	rows, err := s.db.Model(&CancelContractApply{GuildId: guildId}).Select("count(distinct(uid))").
		Where("guild_id=? and apply_time > ? and status in(0,4,6)", guildId, expiredTime).Rows()

	if err != nil {
		log.Errorf("GetGuildCancelContractApplyCnt fail to Select. guildId:%v, err:%v", guildId, err)
		return 0, err
	}
	if rows.Err() != nil {
		log.Errorln(rows.Err())
	}

	cnt := uint32(0)

	defer rows.Close()
	if rows.Next() {
		_ = rows.Scan(&cnt)
	}

	log.Debugf("GetGuildCancelContractApplyCnt guildId:%d, cnt:%+v", guildId, cnt)
	return cnt, nil
}

func (s *Store) GetTimeOutCancelContractApplyList(idx uint32, expiredTime time.Time) ([]*CancelContractApply, error) {
	list := make([]*CancelContractApply, 0)

	err := s.db.Model(&CancelContractApply{GuildId: idx}).
		Select("uid,guild_id,guild_name,status,apply_time").
		Where("apply_time < ? and status in(0,4,6)", expiredTime).
		Scan(&list).Error

	if err != nil {
		log.Errorf("GetTimeOutCancelContractApplyList fail to Select. idx:%+v, expiredTime:%v, err:%v", idx, expiredTime, err)
		return list, err
	}

	//log.Debugf("GetTimeOutCancelContractApplyList idx:%+v, expiredTime:%v, list:%v", idx, expiredTime, list)
	return list, nil
}

func (s *Store) GetNearlyExpireNegotiateCancelContractApplyList(idx uint32, expiredTime time.Time) ([]*CancelContractApply, error) {
	list := make([]*CancelContractApply, 0)

	err := s.db.Model(&CancelContractApply{GuildId: idx}).
		Select("uid,guild_id,guild_name,status,apply_time").
		Where("apply_time < ? and status = 0 and cancel_type = 3", expiredTime).
		Scan(&list).Error

	if err != nil {
		log.Errorf("GetNearlyExpireNegotiateCancelContractApplyList fail to Select. idx:%+v, expiredTime:%v, err:%v", idx, expiredTime, err)
		return list, err
	}

	//log.Debugf("GetTimeOutCancelContractApplyList idx:%+v, expiredTime:%v, list:%v", idx, expiredTime, list)
	return list, nil
}

func (s *Store) GetCancelContractApply(uid, guildId uint32) (bool, *CancelContractApply, error) {
	info := &CancelContractApply{GuildId: guildId}
	err := s.db.Table(info.TableName()).Where("uid=? and guild_id=?", uid, guildId).Scan(info).Error
	if err != nil {
		log.Errorf("GetCancelContractApply fail %v, guildId=%d, uid=%d", err, guildId, uid)
		if gorm.IsRecordNotFoundError(err) {
			return false, nil, nil
		}
		return false, nil, err
	}
	return true, info, nil
}

func (s *Store) UpdateCancelContractApply(tx *gorm.DB, uid, guildId uint32, status uint32) error {
	db := s.db
	if tx != nil {
		db = tx
	}

	m := map[string]interface{}{
		"status": status,
	}
	info := &CancelContractApply{GuildId: guildId}

	res := db.Table(info.TableName()).Where("uid=? and guild_id=? ", uid, guildId).Updates(m)
	if res.RowsAffected != 1 {
		log.Errorf("UpdateCancelContractApply fail to Update. uid:%d, guildId:%d, err:%v", uid, guildId, res.Error)
	}
	return res.Error
}

func (s *Store) UpdateCancelContractApplyVideoKey(tx *gorm.DB, uid, guildId uint32, list string) error {
	db := s.db
	if tx != nil {
		db = tx
	}

	m := map[string]interface{}{
		"video_proof_list": list,
	}
	info := &CancelContractApply{GuildId: guildId}

	res := db.Table(info.TableName()).Where("uid=? and guild_id=? ", uid, guildId).Updates(m)
	if res.RowsAffected != 1 {
		log.Errorf("UpdateCancelContractApplyVideoKey fail to Update. uid:%d, guildId:%d, err:%v", uid, guildId, res.Error)
	}
	return res.Error
}

type CancelReasonSnapshot struct {
	Id      uint32 `db:"id"`  // 自增主键id
	Uid     uint32 `db:"uid"` // 用户id
	GuildId uint32 `db:"guild_id"`
	Content string `db:"content"` // 权益快照内容
}

const CreateCancelReasonSnapshotTbl = `CREATE TABLE IF NOT EXISTS cancel_reason_snapshot (
       	id INT(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键id',
	   	uid INT(11) NOT NULL DEFAULT 0 COMMENT '用户id',
	   	guild_id INT(11) NOT NULL DEFAULT 0 COMMENT '公会id',
	   	content BLOB NOT NULL DEFAULT '' COMMENT '权益快照内容',
	   	PRIMARY KEY (id)
	   	  	) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='从业者类型变更权益快照表';`

func (w *CancelReasonSnapshot) TableName() string {
	return "cancel_reason_snapshot"
}

func (s *Store) AddCancelReasonSnapshot(tx *gorm.DB, info *CancelReasonSnapshot) (uint32, error) {
	db := s.db
	if tx != nil {
		db = tx
	}

	err := db.Create(info).Error
	if err != nil {
		log.ErrorWithCtx(nil, "AddCancelReasonSnapshot Create fail uid:%d, guild:%d, err: %v", info.Uid, info.GuildId, err)
		return 0, err
	}

	return info.Id, nil
}

func (s *Store) GetCancelReasonSnapshot(tx *gorm.DB, id uint32) (*CancelReasonSnapshot, error) {
	db := s.db
	if tx != nil {
		db = tx
	}

	var info CancelReasonSnapshot
	err := db.Where("id = ?", id).First(&info).Error
	// 如果没有结果
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}

	if err != nil {
		return nil, err
	}

	return &info, nil
}

func (s *Store) UpdateCancelReasonSnapshotContent(tx *gorm.DB, id uint32, content string) error {
	db := s.db
	if tx != nil {
		db = tx
	}

	return db.Table("cancel_reason_snapshot").Where("id = ?", id).Update("content", content).Error
}
