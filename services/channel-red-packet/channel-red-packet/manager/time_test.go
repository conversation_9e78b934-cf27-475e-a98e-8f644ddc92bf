package manager

import (
	"context"
	"errors"
	backpackBase "golang.52tt.com/clients/backpack-base"
	numeric_go "golang.52tt.com/clients/numeric-go"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"golang.52tt.com/clients/account"
	apicenter "golang.52tt.com/clients/apicenter/apiserver"
	backpackSender "golang.52tt.com/clients/backpack-sender"
	"golang.52tt.com/clients/channel"
	channelmsgexpress "golang.52tt.com/clients/channel-msg-express"
	"golang.52tt.com/clients/darkserver"
	"golang.52tt.com/clients/nobility"
	PushNotification "golang.52tt.com/clients/push-notification/v2"
	"golang.52tt.com/clients/seqgen/v2"
	unifiedPay "golang.52tt.com/clients/unified_pay"
	userPresent "golang.52tt.com/clients/userpresent"
	ukw "golang.52tt.com/clients/you-know-who"
	"golang.52tt.com/services/channel-red-packet/channel-red-packet/cache"
	"golang.52tt.com/services/channel-red-packet/channel-red-packet/conf"
	"golang.52tt.com/services/channel-red-packet/channel-red-packet/mysql"
)

func TestRedPacketMgr_StartTimer(t *testing.T) {
	type fields struct {
		shutDown      chan interface{}
		sc            conf.IServiceConfigT
		BusinessConf  conf.IBusinessConfManager
		Cache         cache.IRedPacketCache
		Store         mysql.IStore
		apiCenterCli  apicenter.IClient
		seqCli        seqgen.IClient
		pushCli       PushNotification.IClient
		accountCli    account.IClient
		channelMsgCli channelmsgexpress.IClient
		presentCli    userPresent.IClient
		backpackCli   backpackBase.IClient
		bpSendCli     backpackSender.IClient
		numericCli    numeric_go.IClient
		nobilityCli   nobility.IClient
		darkCli       darkserver.IClient
		unifiedPayCli unifiedPay.IClient
		ukwCli        ukw.IClient
		channelCli    channel.IClient
	}
	tests := []struct {
		name   string
		fields fields
	}{
		// TODO: Add test cases.
	}
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*3)
	defer cancel()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &RedPacketMgr{
				shutDown:      tt.fields.shutDown,
				sc:            tt.fields.sc,
				BusinessConf:  tt.fields.BusinessConf,
				Cache:         tt.fields.Cache,
				Store:         tt.fields.Store,
				apiCenterCli:  tt.fields.apiCenterCli,
				seqCli:        tt.fields.seqCli,
				pushCli:       tt.fields.pushCli,
				accountCli:    tt.fields.accountCli,
				channelMsgCli: tt.fields.channelMsgCli,
				presentCli:    tt.fields.presentCli,
				backpackCli:   tt.fields.backpackCli,
				bpSendCli:     tt.fields.bpSendCli,
				numericGoCli:  tt.fields.numericCli,
				nobilityCli:   tt.fields.nobilityCli,
				darkCli:       tt.fields.darkCli,
				unifiedPayCli: tt.fields.unifiedPayCli,
				ukwCli:        tt.fields.ukwCli,
				channelCli:    tt.fields.channelCli,
			}
			_ = m.StartTimer(ctx)
		})
	}
}

func TestRedPacketMgr_CheckRPRainBegin(t *testing.T) {
	type fields struct {
		shutDown      chan interface{}
		sc            conf.IServiceConfigT
		BusinessConf  conf.IBusinessConfManager
		Cache         cache.IRedPacketCache
		Store         mysql.IStore
		apiCenterCli  apicenter.IClient
		seqCli        seqgen.IClient
		pushCli       PushNotification.IClient
		accountCli    account.IClient
		channelMsgCli channelmsgexpress.IClient
		presentCli    userPresent.IClient
		backpackCli   backpackBase.IClient
		bpSendCli     backpackSender.IClient
		numericCli    numeric_go.IClient
		nobilityCli   nobility.IClient
		darkCli       darkserver.IClient
		unifiedPayCli unifiedPay.IClient
		ukwCli        ukw.IClient
		channelCli    channel.IClient
	}
	tests := []struct {
		name   string
		fields fields
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &RedPacketMgr{
				shutDown:      tt.fields.shutDown,
				sc:            tt.fields.sc,
				BusinessConf:  tt.fields.BusinessConf,
				Cache:         tt.fields.Cache,
				Store:         tt.fields.Store,
				apiCenterCli:  tt.fields.apiCenterCli,
				seqCli:        tt.fields.seqCli,
				pushCli:       tt.fields.pushCli,
				accountCli:    tt.fields.accountCli,
				channelMsgCli: tt.fields.channelMsgCli,
				presentCli:    tt.fields.presentCli,
				backpackCli:   tt.fields.backpackCli,
				bpSendCli:     tt.fields.bpSendCli,
				numericGoCli:  tt.fields.numericCli,
				nobilityCli:   tt.fields.nobilityCli,
				darkCli:       tt.fields.darkCli,
				unifiedPayCli: tt.fields.unifiedPayCli,
				ukwCli:        tt.fields.ukwCli,
				channelCli:    tt.fields.channelCli,
			}
			m.CheckRPRainBegin()
		})
	}
}

func TestRedPacketMgr_CheckRPRainEnd(t *testing.T) {
	type fields struct {
		shutDown      chan interface{}
		sc            conf.IServiceConfigT
		BusinessConf  conf.IBusinessConfManager
		Cache         cache.IRedPacketCache
		Store         mysql.IStore
		apiCenterCli  apicenter.IClient
		seqCli        seqgen.IClient
		pushCli       PushNotification.IClient
		accountCli    account.IClient
		channelMsgCli channelmsgexpress.IClient
		presentCli    userPresent.IClient
		backpackCli   backpackBase.IClient
		bpSendCli     backpackSender.IClient
		numericCli    numeric_go.IClient
		nobilityCli   nobility.IClient
		darkCli       darkserver.IClient
		unifiedPayCli unifiedPay.IClient
		ukwCli        ukw.IClient
		channelCli    channel.IClient
	}
	tests := []struct {
		name   string
		fields fields
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &RedPacketMgr{
				shutDown:      tt.fields.shutDown,
				sc:            tt.fields.sc,
				BusinessConf:  tt.fields.BusinessConf,
				Cache:         tt.fields.Cache,
				Store:         tt.fields.Store,
				apiCenterCli:  tt.fields.apiCenterCli,
				seqCli:        tt.fields.seqCli,
				pushCli:       tt.fields.pushCli,
				accountCli:    tt.fields.accountCli,
				channelMsgCli: tt.fields.channelMsgCli,
				presentCli:    tt.fields.presentCli,
				backpackCli:   tt.fields.backpackCli,
				bpSendCli:     tt.fields.bpSendCli,
				numericGoCli:  tt.fields.numericCli,
				nobilityCli:   tt.fields.nobilityCli,
				darkCli:       tt.fields.darkCli,
				unifiedPayCli: tt.fields.unifiedPayCli,
				ukwCli:        tt.fields.ukwCli,
				channelCli:    tt.fields.channelCli,
			}
			m.CheckRPRainEnd()
		})
	}
}

func TestRedPacketMgr_CheckRPSettleAward(t *testing.T) {
	type fields struct {
		shutDown      chan interface{}
		sc            conf.IServiceConfigT
		BusinessConf  conf.IBusinessConfManager
		Cache         cache.IRedPacketCache
		Store         mysql.IStore
		apiCenterCli  apicenter.IClient
		seqCli        seqgen.IClient
		pushCli       PushNotification.IClient
		accountCli    account.IClient
		channelMsgCli channelmsgexpress.IClient
		presentCli    userPresent.IClient
		backpackCli   backpackBase.IClient
		bpSendCli     backpackSender.IClient
		numericCli    numeric_go.IClient
		nobilityCli   nobility.IClient
		darkCli       darkserver.IClient
		unifiedPayCli unifiedPay.IClient
		ukwCli        ukw.IClient
		channelCli    channel.IClient
	}
	tests := []struct {
		name   string
		fields fields
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &RedPacketMgr{
				shutDown:      tt.fields.shutDown,
				sc:            tt.fields.sc,
				BusinessConf:  tt.fields.BusinessConf,
				Cache:         tt.fields.Cache,
				Store:         tt.fields.Store,
				apiCenterCli:  tt.fields.apiCenterCli,
				seqCli:        tt.fields.seqCli,
				pushCli:       tt.fields.pushCli,
				accountCli:    tt.fields.accountCli,
				channelMsgCli: tt.fields.channelMsgCli,
				presentCli:    tt.fields.presentCli,
				backpackCli:   tt.fields.backpackCli,
				bpSendCli:     tt.fields.bpSendCli,
				numericGoCli:  tt.fields.numericCli,
				nobilityCli:   tt.fields.nobilityCli,
				darkCli:       tt.fields.darkCli,
				unifiedPayCli: tt.fields.unifiedPayCli,
				ukwCli:        tt.fields.ukwCli,
				channelCli:    tt.fields.channelCli,
			}
			m.CheckRPSettleAward()
		})
	}
}

func TestRedPacketMgr_CheckRPPhraseChange(t *testing.T) {

	genMgrMock(t)

	handle := func(ctx context.Context, orderId string) error { return nil }

	orderIdList := []string{}

	gomock.InOrder(

		mockCache.EXPECT().GetRedisCurrTime().Return(time.Now(), errors.New("!")),

		mockCache.EXPECT().GetRedisCurrTime().Return(time.Now(), nil),
		mockCache.EXPECT().PopExpireMem(gomock.Any(), gomock.Any(), gomock.Any()).Return(orderIdList, nil),
	)

	type fields struct {
		shutDown      chan interface{}
		sc            conf.IServiceConfigT
		BusinessConf  conf.IBusinessConfManager
		Cache         cache.IRedPacketCache
		Store         mysql.IStore
		apiCenterCli  apicenter.IClient
		seqCli        seqgen.IClient
		pushCli       PushNotification.IClient
		accountCli    account.IClient
		channelMsgCli channelmsgexpress.IClient
		presentCli    userPresent.IClient
		backpackCli   backpackBase.IClient
		bpSendCli     backpackSender.IClient
		numericCli    numeric_go.IClient
		nobilityCli   nobility.IClient
		darkCli       darkserver.IClient
		unifiedPayCli unifiedPay.IClient
		ukwCli        ukw.IClient
		channelCli    channel.IClient
	}
	type args struct {
		key     string
		timeout time.Duration
		handle  func(ctx context.Context, orderId string) error
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			fields: fields{
				shutDown:     make(chan interface{}),
				sc:           mockSc,
				BusinessConf: mockBusinessConf,
				Cache:        mockCache,
				Store:        mockStore,
				apiCenterCli: mockapiCenterCli,
				//seqCli:        mockseqCli,
				pushCli:       mockpushCli,
				accountCli:    mockaccountCli,
				channelMsgCli: mockchannelMsgCli,
				presentCli:    mockpresentCli,
				backpackCli:   mockbackpackCli,
				bpSendCli:     mockbpSendCli,
				numericCli:    mocknumericCli,
				nobilityCli:   mocknobilityCli,
				darkCli:       mockdarkCli,
				unifiedPayCli: mockunifiedPayCli,
				ukwCli:        mockukwCli,
				channelCli:    mockchannelCli,
			},
			args: args{
				key:     "1",
				timeout: time.Second,
				handle:  handle,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &RedPacketMgr{
				shutDown:      tt.fields.shutDown,
				sc:            tt.fields.sc,
				BusinessConf:  tt.fields.BusinessConf,
				Cache:         tt.fields.Cache,
				Store:         tt.fields.Store,
				apiCenterCli:  tt.fields.apiCenterCli,
				seqCli:        tt.fields.seqCli,
				pushCli:       tt.fields.pushCli,
				accountCli:    tt.fields.accountCli,
				channelMsgCli: tt.fields.channelMsgCli,
				presentCli:    tt.fields.presentCli,
				backpackCli:   tt.fields.backpackCli,
				bpSendCli:     tt.fields.bpSendCli,
				numericGoCli:  tt.fields.numericCli,
				nobilityCli:   tt.fields.nobilityCli,
				darkCli:       tt.fields.darkCli,
				unifiedPayCli: tt.fields.unifiedPayCli,
				ukwCli:        tt.fields.ukwCli,
				channelCli:    tt.fields.channelCli,
			}
			m.CheckRPPhraseChange(tt.args.key, tt.args.timeout, tt.args.handle)
		})
	}
}

func TestRedPacketMgr_HandleRPRainBegin(t *testing.T) {
	type fields struct {
		shutDown      chan interface{}
		sc            conf.IServiceConfigT
		BusinessConf  conf.IBusinessConfManager
		Cache         cache.IRedPacketCache
		Store         mysql.IStore
		apiCenterCli  apicenter.IClient
		seqCli        seqgen.IClient
		pushCli       PushNotification.IClient
		accountCli    account.IClient
		channelMsgCli channelmsgexpress.IClient
		presentCli    userPresent.IClient
		backpackCli   backpackBase.IClient
		bpSendCli     backpackSender.IClient
		numericCli    numeric_go.IClient
		nobilityCli   nobility.IClient
		darkCli       darkserver.IClient
		unifiedPayCli unifiedPay.IClient
		ukwCli        ukw.IClient
		channelCli    channel.IClient
	}
	type args struct {
		ctx     context.Context
		orderId string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &RedPacketMgr{
				shutDown:      tt.fields.shutDown,
				sc:            tt.fields.sc,
				BusinessConf:  tt.fields.BusinessConf,
				Cache:         tt.fields.Cache,
				Store:         tt.fields.Store,
				apiCenterCli:  tt.fields.apiCenterCli,
				seqCli:        tt.fields.seqCli,
				pushCli:       tt.fields.pushCli,
				accountCli:    tt.fields.accountCli,
				channelMsgCli: tt.fields.channelMsgCli,
				presentCli:    tt.fields.presentCli,
				backpackCli:   tt.fields.backpackCli,
				bpSendCli:     tt.fields.bpSendCli,
				numericGoCli:  tt.fields.numericCli,
				nobilityCli:   tt.fields.nobilityCli,
				darkCli:       tt.fields.darkCli,
				unifiedPayCli: tt.fields.unifiedPayCli,
				ukwCli:        tt.fields.ukwCli,
				channelCli:    tt.fields.channelCli,
			}
			if err := m.HandleRPRainBegin(tt.args.ctx, tt.args.orderId); (err != nil) != tt.wantErr {
				t.Errorf("RedPacketMgr.HandleRPRainBegin() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestRedPacketMgr_HandleRPRainEnd(t *testing.T) {

	genMgrMock(t)

	type fields struct {
		shutDown      chan interface{}
		sc            conf.IServiceConfigT
		BusinessConf  conf.IBusinessConfManager
		Cache         cache.IRedPacketCache
		Store         mysql.IStore
		apiCenterCli  apicenter.IClient
		seqCli        seqgen.IClient
		pushCli       PushNotification.IClient
		accountCli    account.IClient
		channelMsgCli channelmsgexpress.IClient
		presentCli    userPresent.IClient
		backpackCli   backpackBase.IClient
		bpSendCli     backpackSender.IClient
		numericCli    numeric_go.IClient
		nobilityCli   nobility.IClient
		darkCli       darkserver.IClient
		unifiedPayCli unifiedPay.IClient
		ukwCli        ukw.IClient
		channelCli    channel.IClient
	}
	type args struct {
		ctx     context.Context
		orderId string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &RedPacketMgr{
				shutDown:      tt.fields.shutDown,
				sc:            tt.fields.sc,
				BusinessConf:  tt.fields.BusinessConf,
				Cache:         tt.fields.Cache,
				Store:         tt.fields.Store,
				apiCenterCli:  tt.fields.apiCenterCli,
				seqCli:        tt.fields.seqCli,
				pushCli:       tt.fields.pushCli,
				accountCli:    tt.fields.accountCli,
				channelMsgCli: tt.fields.channelMsgCli,
				presentCli:    tt.fields.presentCli,
				backpackCli:   tt.fields.backpackCli,
				bpSendCli:     tt.fields.bpSendCli,
				numericGoCli:  tt.fields.numericCli,
				nobilityCli:   tt.fields.nobilityCli,
				darkCli:       tt.fields.darkCli,
				unifiedPayCli: tt.fields.unifiedPayCli,
				ukwCli:        tt.fields.ukwCli,
				channelCli:    tt.fields.channelCli,
			}
			if err := m.HandleRPRainEnd(tt.args.ctx, tt.args.orderId); (err != nil) != tt.wantErr {
				t.Errorf("RedPacketMgr.HandleRPRainEnd() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestRedPacketMgr_CheckAwardDone(t *testing.T) {
	type fields struct {
		shutDown      chan interface{}
		sc            conf.IServiceConfigT
		BusinessConf  conf.IBusinessConfManager
		Cache         cache.IRedPacketCache
		Store         mysql.IStore
		apiCenterCli  apicenter.IClient
		seqCli        seqgen.IClient
		pushCli       PushNotification.IClient
		accountCli    account.IClient
		channelMsgCli channelmsgexpress.IClient
		presentCli    userPresent.IClient
		backpackCli   backpackBase.IClient
		bpSendCli     backpackSender.IClient
		numericCli    numeric_go.IClient
		nobilityCli   nobility.IClient
		darkCli       darkserver.IClient
		unifiedPayCli unifiedPay.IClient
		ukwCli        ukw.IClient
		channelCli    channel.IClient
	}
	tests := []struct {
		name   string
		fields fields
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &RedPacketMgr{
				shutDown:      tt.fields.shutDown,
				sc:            tt.fields.sc,
				BusinessConf:  tt.fields.BusinessConf,
				Cache:         tt.fields.Cache,
				Store:         tt.fields.Store,
				apiCenterCli:  tt.fields.apiCenterCli,
				seqCli:        tt.fields.seqCli,
				pushCli:       tt.fields.pushCli,
				accountCli:    tt.fields.accountCli,
				channelMsgCli: tt.fields.channelMsgCli,
				presentCli:    tt.fields.presentCli,
				backpackCli:   tt.fields.backpackCli,
				bpSendCli:     tt.fields.bpSendCli,
				numericGoCli:  tt.fields.numericCli,
				nobilityCli:   tt.fields.nobilityCli,
				darkCli:       tt.fields.darkCli,
				unifiedPayCli: tt.fields.unifiedPayCli,
				ukwCli:        tt.fields.ukwCli,
				channelCli:    tt.fields.channelCli,
			}
			m.CheckAwardDone()
		})
	}
}

func TestRedPacketMgr_TimerHandle(t *testing.T) {
	type fields struct {
		shutDown      chan interface{}
		sc            conf.IServiceConfigT
		BusinessConf  conf.IBusinessConfManager
		Cache         cache.IRedPacketCache
		Store         mysql.IStore
		apiCenterCli  apicenter.IClient
		seqCli        seqgen.IClient
		pushCli       PushNotification.IClient
		accountCli    account.IClient
		channelMsgCli channelmsgexpress.IClient
		presentCli    userPresent.IClient
		backpackCli   backpackBase.IClient
		bpSendCli     backpackSender.IClient
		numericCli    numeric_go.IClient
		nobilityCli   nobility.IClient
		darkCli       darkserver.IClient
		unifiedPayCli unifiedPay.IClient
		ukwCli        ukw.IClient
		channelCli    channel.IClient
	}
	type args struct {
		d      time.Duration
		handle func()
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &RedPacketMgr{
				shutDown:      tt.fields.shutDown,
				sc:            tt.fields.sc,
				BusinessConf:  tt.fields.BusinessConf,
				Cache:         tt.fields.Cache,
				Store:         tt.fields.Store,
				apiCenterCli:  tt.fields.apiCenterCli,
				seqCli:        tt.fields.seqCli,
				pushCli:       tt.fields.pushCli,
				accountCli:    tt.fields.accountCli,
				channelMsgCli: tt.fields.channelMsgCli,
				presentCli:    tt.fields.presentCli,
				backpackCli:   tt.fields.backpackCli,
				bpSendCli:     tt.fields.bpSendCli,
				numericGoCli:  tt.fields.numericCli,
				nobilityCli:   tt.fields.nobilityCli,
				darkCli:       tt.fields.darkCli,
				unifiedPayCli: tt.fields.unifiedPayCli,
				ukwCli:        tt.fields.ukwCli,
				channelCli:    tt.fields.channelCli,
			}
			m.TimerHandle(tt.args.d, tt.args.handle)
		})
	}
}

func TestRedPacketMgr_handleWithPanicCatch(t *testing.T) {
	type fields struct {
		shutDown      chan interface{}
		sc            conf.IServiceConfigT
		BusinessConf  conf.IBusinessConfManager
		Cache         cache.IRedPacketCache
		Store         mysql.IStore
		apiCenterCli  apicenter.IClient
		seqCli        seqgen.IClient
		pushCli       PushNotification.IClient
		accountCli    account.IClient
		channelMsgCli channelmsgexpress.IClient
		presentCli    userPresent.IClient
		backpackCli   backpackBase.IClient
		bpSendCli     backpackSender.IClient
		numericCli    numeric_go.IClient
		nobilityCli   nobility.IClient
		darkCli       darkserver.IClient
		unifiedPayCli unifiedPay.IClient
		ukwCli        ukw.IClient
		channelCli    channel.IClient
	}
	type args struct {
		handle func()
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &RedPacketMgr{
				shutDown:      tt.fields.shutDown,
				sc:            tt.fields.sc,
				BusinessConf:  tt.fields.BusinessConf,
				Cache:         tt.fields.Cache,
				Store:         tt.fields.Store,
				apiCenterCli:  tt.fields.apiCenterCli,
				seqCli:        tt.fields.seqCli,
				pushCli:       tt.fields.pushCli,
				accountCli:    tt.fields.accountCli,
				channelMsgCli: tt.fields.channelMsgCli,
				presentCli:    tt.fields.presentCli,
				backpackCli:   tt.fields.backpackCli,
				bpSendCli:     tt.fields.bpSendCli,
				numericGoCli:  tt.fields.numericCli,
				nobilityCli:   tt.fields.nobilityCli,
				darkCli:       tt.fields.darkCli,
				unifiedPayCli: tt.fields.unifiedPayCli,
				ukwCli:        tt.fields.ukwCli,
				channelCli:    tt.fields.channelCli,
			}
			m.handleWithPanicCatch(tt.args.handle)
		})
	}
}
