syntax = "proto2";

package ga.myinfo;

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/myinfo";

import "ga_base.proto";

//修改手游宝账号
message ModifyUserNameReq{
	required BaseReq base_req = 1;
	required string new_user_name = 2;
}

message ModifyUserNameResp{
	required BaseResp base_resp = 1;
}

//修改昵称
message ModifyNicknameReq{
	required BaseReq base_req = 1;
	required string new_nickname = 2;
	optional bool prefix_valid = 3;
}

message ModifyNicknameResp{
	required BaseResp base_resp = 1;
}

//修改签名
message ModifySignatureReq{
	required BaseReq base_req = 1;
	required string new_signature = 2;
}

message ModifySignatureResp{
	required BaseResp base_resp = 1;
}


// 批量获取用户grow info
message BatGetUserGrowInfoReq {
	required BaseReq base_req = 1;
	repeated uint32 uid_list = 2;
}

message BatGetUserGrowInfoResp {
	required BaseResp base_resp = 1;
	repeated GrowInfo grow_info_list = 2;
}

// 设置勋章尾灯
message SetMyMedalTaillightReq
{
	required BaseReq base_req = 1;
	repeated uint32 medal_taillight_list = 2;
}

message SetMyMedalTaillightResp
{
	required BaseResp base_resp = 1;
	repeated uint32 medal_taillight_list = 2;
}


message UpdatePhotoAlbumReq {
	required BaseReq base_req = 1;
	required uint32 uid = 2;
	repeated string img_key_list = 3;
	repeated string new_img_key_list = 4; /* 新版obs，全量key */
}


message UpdatePhotoAlbumResp {
	required BaseResp base_resp = 1;
}


message GetPhotoAlbumReq {
	required BaseReq base_req = 1;
	required uint32 uid = 2;
}

message PhotoAlbumNewURLInfo{
	required string key = 1;
	required string url = 2;
}
message GetPhotoAlbumResp {
	required BaseResp base_resp = 1;
	repeated string img_key_list = 2;
	repeated string img_url_list = 3; // 新版 完整url 包含旧image
	repeated PhotoAlbumNewURLInfo all_photo_list = 4;
}

message SetUserHeadwearReq{
	required BaseReq base_req = 1;
	required uint32 headwear_id = 2;
	optional uint32 cp_uid = 3;
	optional string custom_text = 4; // 自定义文案
}

message SetUserHeadwearResp{
	required BaseResp base_resp = 1;
	required UserHeadwearInfo headwear_info = 2;
}

message GetUserHeadwearReq{
	required BaseReq base_req = 1;
}

message GetUserHeadwearResp{
	required BaseResp base_resp = 1;
	repeated UserHeadwearInfo headwear_list = 2;
	required uint32 headwear_id_inuse = 3; //正在使用的头像框ID, 0表示没有使用
	optional uint32 cp_uid_inuse = 4;      //正在使用的头像框绑定的cp uid ，0表示无
	optional string custom_text = 5;       //正在使用的头像框对应的自定义文案
	optional string custom_headwear_text = 6;       //如果是自定义装扮，在图表处应该显示的文案
}

message RemoveUserHeadwearReq{
	required BaseReq base_req = 1;
	required uint32 headwear_id = 2;
}

message RemoveUserHeadwearResp{
	required BaseResp base_resp = 1;
}

//用户官方认证 cmd 231
message GetUserCertificationReq {
	required BaseReq base_req = 1;
	required string target_account = 2; // 目标ttid
	optional uint32 request_type = 3; // see LocationType
}

message GetUserCertificationResp {
	required BaseResp base_resp = 1;
	required string title = 2;  // 官方认证
	required string intro = 3;  // 认证介绍

	optional string style = 4;  //

	optional string certify_special_effect_icon = 5;  // 大v图标动效
	optional string certify_special_effect_title = 6; // 带有文字背景
}

message UserCertificationInfo {
	required uint32 id = 1;
	required string title = 2; // 官方认证标题
	required string intro = 3; // 认证介绍
	required string style = 4; // 认证类型
	required uint64 begin_ts = 5; // 开始时间
	required uint64 end_ts = 6;   // 结束时间
	required bool is_use = 7;     // 是否正在佩戴

	optional string certify_special_effect_icon = 8;  // 大v图标动效
	optional string certify_special_effect_title = 9; // 带有文字背景
}

enum LocationType {
	Personal_Page = 0; // 个人主页
	Channel = 1;     // 房间个人资料卡
	Anchor_Card = 2;   // 主播资料卡
	PersonalityDress = 3; // 个性装扮 图标
}

// 获取用户的认证列表 cmd 233
message GetUserCertifyListReq  {
	required BaseReq base_req = 1;
	optional uint32 target_uid = 2;
	optional uint32 request_type = 3; // see LocationType
	optional string target_account = 4; // 当target_uid不传时，传入目标ttid
}

message GetUserCertifyListResp {
	required BaseResp base_resp = 1;
	repeated UserCertificationInfo certify_list = 2;
}

// 设置用户佩戴的认证样式
message SetUserWearCertificationReq {
	required BaseReq base_req = 1;
	required uint32 id = 2;
}

message SetUserWearCertificationResp {
	required BaseResp base_resp = 1;
}


// 用户服务协议
message UserContractInfo {
	enum UserContractType {
		INVALID_CONTRACT_TYPE = 0;
		TT_SERVICE_CONTRACT = 1;  // TT服务协议
	}

	required uint32 contract_type = 1;  // UserContractType
	required string contract_url = 2;
	required string contract_version = 3;
	optional bool is_agree = 4;
}

message GetUserContractInfoReq {
	required BaseReq base_req = 1;
	required uint32 contract_type = 2;  // UserContractType
}

message GetUserContractInfoResp {
	required BaseResp base_resp = 1;
	optional UserContractInfo contract_info = 2;
}

message AgreeUserContractReq {
	required BaseReq base_req = 1;
	required uint32 contract_type = 2;  // UserContractType
	required string contract_version = 3;
}

message AgreeUserContractResp {
	required BaseResp base_resp = 1;
}

message BatchGetNobilityInfosReq{
	required BaseReq base_req = 1;
	repeated uint32 uids = 2;
	repeated string accounts = 3;
}

message BatchGetNobilityInfosResp
{
	required BaseResp base_resp = 1;
	repeated NobilityInfo nobility_infos = 2;
	repeated NobilityInfo account_nobility_infos = 3;
}

// 贵族周期信息
message NobilityCycleInfo {
	required uint32 remain_days = 1;    // 周期剩余天数
	required string float_call = 2;    // 浮层提醒称呼
	required string float_privilege = 3;  //浮层提醒特权文案
	required uint32 now_ts = 4;
}


// 获取贵族相关信息，本来想搞成获取贵族信息的通用接口, 由于实现逻辑问题，这个接口只用来获取贵族保级提醒信息
message GetNobilityInfoReq {
	required BaseReq base_req = 1;
	required uint32 uid = 2;
}
message GetNobilityInfoResp {
	required BaseResp base_resp = 1;
	optional NobilityCycleInfo cycle_info = 2;
	optional bool is_keep = 3;  //是否保级成功
}

// 获取用户贵族详细信息
message GetNobilityDetailInfoReq
{
	required BaseReq base_req = 1;
	required uint32 uid = 2;
}
message GetNobilityDetailInfoResp
{
	required BaseResp base_resp = 1;
	optional NobilityInfo info = 2;  // 贵族信息
} 
